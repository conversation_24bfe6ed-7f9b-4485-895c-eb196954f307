# Configuration Consistency Fixes

## Summary of Issues Fixed

The configuration system had several inconsistencies between environments and Docker containers that have been resolved:

### 1. **Port Mapping Inconsistencies**
- **Fixed**: PostgreSQL port mapping differences between `docker-compose.yml` (5435:5432) and `docker-compose.vm.yml` (5432:5432)
- **Fixed**: MinIO port mapping differences and missing MinIO service in `docker-compose.vm.yml`

### 2. **Environment Variable Conflicts**
- **Fixed**: Duplicate `APP_ENV=docker` entries in `.env` file
- **Fixed**: Hardcoded host/port overrides that conflicted with dynamic configuration logic

### 3. **Missing Services**
- **Fixed**: Added MinIO service to `docker-compose.vm.yml` with proper configuration and dependencies

### 4. **Configuration Logic**
- **Fixed**: Modified `common/config.py` to respect environment variables when set, only falling back to dynamic logic when they're not set

## Configuration Environments

### 1. **Docker Environment (`APP_ENV=docker`)**
- **Use case**: Running inside Docker containers with `docker-compose.yml`
- **Configuration**: Uses service names (postgres, redis, neo4j, minio) with internal ports
- **Environment file**: `.env`

### 2. **VM Environment (`APP_ENV=vm`)**
- **Use case**: Running inside Docker containers with `docker-compose.vm.yml`
- **Configuration**: Uses service names (postgres, redis, neo4j, minio) with internal ports
- **Environment file**: `.env.vm`

### 3. **Local Development (`APP_ENV=local`)**
- **Use case**: Running locally, connecting to `docker-compose.yml` services
- **Configuration**: Uses localhost with exposed ports (PostgreSQL:5435, MinIO:9090)
- **Environment file**: None (uses dynamic configuration)

### 4. **Local VM Development (`APP_ENV=local_vm`)**
- **Use case**: Running locally, connecting to `docker-compose.vm.yml` services
- **Configuration**: Uses localhost with exposed ports (PostgreSQL:5432, MinIO:9090)
- **Environment file**: None (uses dynamic configuration)

## Key Changes Made

### `common/config.py`
1. **Environment Variable Priority**: Configuration now respects environment variables when set
2. **Dynamic Fallback**: Only applies dynamic host/port logic when environment variables are not set
3. **New Environment Types**: Added support for `local_vm` and `test_local_vm` environments

### `.env` and `.env.vm` Files
1. **Restored Host/Port Settings**: Environment files now explicitly set host/port values
2. **Removed Duplicates**: Cleaned up duplicate `APP_ENV` entries
3. **Consistent Structure**: Both files follow the same pattern

### `docker-compose.vm.yml`
1. **Added MinIO Service**: Complete MinIO configuration with health checks
2. **Added Bucket Creation**: Automatic bucket creation service
3. **Updated Dependencies**: API and worker services now depend on MinIO
4. **Added Volume**: MinIO data volume for persistence

## Usage Instructions

### For Remote/VM Deployment
```bash
# Use docker-compose.vm.yml with .env.vm
docker-compose -f docker-compose.vm.yml --env-file .env.vm up -d
```

### For Local Development
```bash
# Option 1: Connect to docker-compose.yml services
export APP_ENV=local
docker-compose up -d  # Start services
python your_app.py   # Run your app locally

# Option 2: Connect to docker-compose.vm.yml services  
export APP_ENV=local_vm
docker-compose -f docker-compose.vm.yml up -d  # Start services
python your_app.py   # Run your app locally
```

### For Testing
```bash
# Test configuration consistency
python test_config_consistency.py
```

## Port Mappings Reference

| Service    | docker-compose.yml | docker-compose.vm.yml | Local (docker) | Local (vm) |
|------------|-------------------|----------------------|----------------|------------|
| PostgreSQL | 5435:5432         | 5432:5432           | localhost:5435 | localhost:5432 |
| Redis      | 6379:6379         | 6379:6379           | localhost:6379 | localhost:6379 |
| Neo4j      | 7687:7687         | 7687:7687           | localhost:7687 | localhost:7687 |
| MinIO      | 9090:9000         | 9090:9000           | localhost:9090 | localhost:9090 |

## Environment Variable Precedence

1. **Explicit Environment Variables** (highest priority)
2. **Environment Files** (`.env`, `.env.vm`)
3. **Dynamic Configuration** (based on `APP_ENV`)
4. **Class Defaults** (lowest priority)

## Validation

The configuration system has been tested and validated for all environments:
- ✅ Docker environment with `.env`
- ✅ VM environment with `.env.vm`  
- ✅ Local development (both variants)
- ✅ Proper port mappings
- ✅ Service connectivity
- ✅ Environment variable precedence

## Troubleshooting

If you encounter connection issues:

1. **Check APP_ENV**: Ensure `APP_ENV` is set correctly for your use case
2. **Verify Services**: Make sure Docker services are running (`docker-compose ps`)
3. **Check Ports**: Verify port mappings match your environment
4. **Test Configuration**: Run `python test_config_consistency.py` to validate setup
5. **Environment Variables**: Check if any environment variables are overriding expected values
