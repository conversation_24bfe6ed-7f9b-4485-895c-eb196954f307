#!/usr/bin/env python3
"""
Test PostgreSQL connections from localhost.
"""
import psycopg2

def test_connections():
    """Test PostgreSQL connections."""
    
    # Read password from .env file
    password = None
    try:
        with open('.env', 'r') as f:
            for line in f:
                if line.startswith('POSTGRES_PASSWORD='):
                    password = line.split('=', 1)[1].strip()
                    break
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return
    
    if not password:
        print("❌ POSTGRES_PASSWORD not found in .env file")
        return
    
    print(f"🔑 Using password from .env file")
    
    # Test 1: Direct connection to localhost
    print("\n1. Testing connection to localhost:5432...")
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='longevity',
            user='longevity',
            password=password
        )
        print('✅ localhost connection successful')
        
        # Test a simple query
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM documents")
        doc_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM processing_tasks")
        task_count = cursor.fetchone()[0]
        print(f"📊 Database status: {doc_count} documents, {task_count} tasks")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'❌ localhost connection failed: {e}')
    
    # Test 2: Connection to 'postgres' hostname (should fail from localhost)
    print("\n2. Testing connection to postgres:5432...")
    try:
        conn = psycopg2.connect(
            host='postgres',
            port='5432',
            database='longevity',
            user='longevity',
            password=password
        )
        print('✅ postgres hostname connection successful')
        conn.close()
    except Exception as e:
        print(f'❌ postgres hostname connection failed: {e}')
        print("   (This is expected when running from localhost)")

if __name__ == "__main__":
    test_connections()
