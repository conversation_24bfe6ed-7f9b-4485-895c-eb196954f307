#!/usr/bin/env python3
"""
Test PostgreSQL and Neo4j connections from localhost.
"""
import psycopg2

def load_env_vars():
    """Load environment variables from .env file."""
    env_vars = {}
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value.strip('"\'')
        return env_vars
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return {}

def test_postgres_connections(env_vars):
    """Test PostgreSQL connections."""
    postgres_password = env_vars.get('POSTGRES_PASSWORD')
    postgres_user = env_vars.get('POSTGRES_USER', 'longevity')
    postgres_db = env_vars.get('POSTGRES_DB', 'longevity')
    
    if not postgres_password:
        print("❌ POSTGRES_PASSWORD not found in .env file")
        return False
    
    print(f"🔑 PostgreSQL credentials: user={postgres_user}, db={postgres_db}")
    
    # Test 1: Connection to localhost (correct for local testing)
    print("\n1. Testing PostgreSQL connection to localhost:5432...")
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database=postgres_db,
            user=postgres_user,
            password=postgres_password
        )
        print('✅ PostgreSQL localhost connection successful')
        
        # Test a simple query
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM documents")
        doc_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM processing_tasks")
        task_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM chunks")
        chunk_count = cursor.fetchone()[0]
        print(f"📊 Database status: {doc_count} documents, {task_count} tasks, {chunk_count} chunks")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f'❌ PostgreSQL localhost connection failed: {e}')
        return False

def test_neo4j_connections(env_vars):
    """Test Neo4j connections."""
    try:
        from neo4j import GraphDatabase
    except ImportError:
        print("⚠️  neo4j driver not available - skipping Neo4j test")
        return True
    
    neo4j_auth = env_vars.get('NEO4J_AUTH')
    if not neo4j_auth:
        print("❌ NEO4J_AUTH not found in .env file")
        return False
    
    # Parse NEO4J_AUTH (format: user/password)
    if '/' in neo4j_auth:
        neo4j_user, neo4j_password = neo4j_auth.split('/', 1)
    else:
        print("❌ Invalid NEO4J_AUTH format (expected: user/password)")
        return False
    
    print(f"🔑 Neo4j credentials: user={neo4j_user}")
    
    # Test connection to localhost (correct for local testing)
    print("\n2. Testing Neo4j connection to localhost:7687...")
    try:
        neo4j_uri = "neo4j://localhost:7687"
        driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        
        with driver.session() as session:
            # Test a simple query
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()["node_count"]
            
            result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
            rel_count = result.single()["rel_count"]
            
            print('✅ Neo4j localhost connection successful')
            print(f"📊 Neo4j status: {node_count} nodes, {rel_count} relationships")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f'❌ Neo4j localhost connection failed: {e}')
        return False

def test_config_with_local_env():
    """Test the config system with APP_ENV=local."""
    print("\n3. Testing config system with APP_ENV=local...")
    try:
        import os
        os.environ['APP_ENV'] = 'local'
        
        from common.config import get_settings
        settings = get_settings()
        
        print(f"📋 Config settings:")
        print(f"   APP_ENV: {settings.APP_ENV}")
        print(f"   POSTGRES_HOST: {settings.POSTGRES_HOST}")
        print(f"   POSTGRES_PORT: {settings.POSTGRES_PORT}")
        print(f"   NEO4J_HOST: {settings.NEO4J_HOST}")
        print(f"   NEO4J_PORT: {settings.NEO4J_PORT}")
        print(f"   DATABASE_URL: {settings.DATABASE_URL}")
        print(f"   NEO4J_URI: {settings.NEO4J_URI}")
        
        # Check if hosts are correctly set to localhost
        if settings.POSTGRES_HOST == 'localhost' and settings.NEO4J_HOST == 'localhost':
            print("✅ Config correctly uses localhost for local environment")
            return True
        else:
            print("❌ Config not using localhost for local environment")
            return False
            
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def main():
    """Main function."""
    print("🚀 Testing local database connections...")
    print("=" * 60)
    
    # Load environment variables
    env_vars = load_env_vars()
    if not env_vars:
        return 1
    
    print(f"📁 Loaded {len(env_vars)} environment variables from .env")
    
    # Test connections
    postgres_ok = test_postgres_connections(env_vars)
    neo4j_ok = test_neo4j_connections(env_vars)
    config_ok = test_config_with_local_env()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"   PostgreSQL: {'✅ OK' if postgres_ok else '❌ FAILED'}")
    print(f"   Neo4j: {'✅ OK' if neo4j_ok else '❌ FAILED'}")
    print(f"   Config: {'✅ OK' if config_ok else '❌ FAILED'}")
    
    if postgres_ok and neo4j_ok and config_ok:
        print("\n🎉 All local connections working!")
        return 0
    else:
        print("\n❌ Some connections failed!")
        return 1

if __name__ == "__main__":
    exit(main())
