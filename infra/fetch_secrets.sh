#!/bin/bash
# infra/fetch_secrets.sh
# Fetch secrets from Google Secret Manager and merge with .env.vm to create .env

set -e

ENV_VM_FILE=".env.vm"
ENV_FILE=".env"

# Check for gcloud CLI
if ! command -v gcloud &> /dev/null; then
  echo "gcloud CLI not found. Please install and authenticate gcloud."
  exit 1
fi

# Fetch secrets from Google Secret Manager
POSTGRES_PASSWORD=$(gcloud secrets versions access latest --secret="longevity-postgres-password")
NEO4J_AUTH=$(gcloud secrets versions access latest --secret="longevity-neo4j-auth")
REDIS_PASSWORD=$(gcloud secrets versions access latest --secret="longevity-redis-password")

# Copy non-secret variables
cp "$ENV_VM_FILE" "$ENV_FILE"

# Append secrets
echo "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" >> "$ENV_FILE"
echo "NEO4J_AUTH=$NEO4J_AUTH" >> "$ENV_FILE"
echo "REDIS_PASSWORD=$REDIS_PASSWORD" >> "$ENV_FILE"

# Optionally export for current shell
export $(grep -v '^#' "$ENV_FILE" | xargs)

echo ".env file created/updated with secrets from Google Secret Manager."