#!/usr/bin/env python3
"""
Quick test to verify the fixes work.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_task_type_fix():
    """Test that TaskType.KNOWLEDGE_GRAPH_BUILDING is available."""
    try:
        from common.task_definitions import TaskType
        
        print("✅ TaskType import successful")
        
        # Test the specific enum value that was missing
        kg_building = TaskType.KNOWLEDGE_GRAPH_BUILDING
        print(f"✅ KNOWLEDGE_GRAPH_BUILDING: {kg_building.value}")
        
        # Test other task types
        print(f"✅ VERTEX_AI_BATCH_JOB: {TaskType.VERTEX_AI_BATCH_JOB.value}")
        print(f"✅ KNOWLEDGE_GRAPH: {TaskType.KNOWLEDGE_GRAPH.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ TaskType test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_fix():
    """Test that Settings class accepts APP_ENV."""
    try:
        from common.config import settings
        
        print("✅ Settings import successful")
        print(f"✅ APP_ENV: {settings.APP_ENV}")
        print(f"✅ Database URL: {settings.DATABASE_URL}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_graph_service():
    """Test that KnowledgeGraphService can access the TaskType."""
    try:
        from services.knowledge_graph_service import KnowledgeGraphService
        from common.task_definitions import TaskType
        
        print("✅ KnowledgeGraphService import successful")
        
        # Test that the service can access the TaskType
        task_type = TaskType.KNOWLEDGE_GRAPH_BUILDING.value
        print(f"✅ Service can access KNOWLEDGE_GRAPH_BUILDING: {task_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ KnowledgeGraphService test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Testing fixes...")
    print()
    
    tests = [
        ("TaskType Fix", test_task_type_fix),
        ("Config Fix", test_config_fix),
        ("KnowledgeGraphService Fix", test_knowledge_graph_service)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: FAILED with exception: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes are working!")
        return 0
    else:
        print("⚠️  Some fixes need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
