"""
Data transport layer for coordinating storage and database operations.
"""
import os
import uuid
import hashlib
import tempfile
import logging
from datetime import datetime, timedelta # Added
from typing import Optional, Dict, Any, List, Union, BinaryIO, Tuple # List, Dict, Any might be here or added

from sqlalchemy.orm import Session
from sqlalchemy import create_engine, and_ # Added and_
from sqlalchemy.orm import sessionmaker

from common.database import get_db, ProcessingTask # Added ProcessingTask
from common.config import settings
from transport.storage_client import StorageClient
from transport.database_client import DatabaseClient
from transport.neo4j_client import Neo4jClient

# Configure logging
logger = logging.getLogger(__name__)


class DataTransport:
    """
    Data transport layer for managing document storage and retrieval.
    Coordinates operations between storage and database.
    """

    def __init__(self, db_session: Optional[Session] = None):
        """
        Initialize data transport with database session.

        Args:
            db_session: SQLAlchemy database session (optional)
        """
        self.db_session = db_session
        self.storage_client = StorageClient()
        self.neo4j_client = Neo4jClient()

    def __enter__(self):
        """Context manager enter method."""
        if self.db_session is None:
            # Use the correct database connection settings
            db_url = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
            logger.debug(f"Connecting to database: {settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}")

            # Create a new engine and session
            engine = create_engine(db_url)
            SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
            self.db_session = SessionLocal()

        self.db_client = DatabaseClient(self.db_session)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit method."""
        if self.db_session:
            self.db_session.close()

        # Close Neo4j client
        self.neo4j_client.close()

    def _generate_document_hash(self, file_obj: BinaryIO) -> str:
        """
        Generate a SHA-256 hash of the document content.

        Args:
            file_obj: File object

        Returns:
            str: SHA-256 hash of document content
        """
        # Save current position
        current_position = file_obj.tell()

        # Reset to beginning of file
        file_obj.seek(0)

        # Create hash object
        hash_obj = hashlib.sha256()

        # Read file in chunks to avoid memory issues with large files
        chunk_size = 8192  # 8KB chunks
        while True:
            data = file_obj.read(chunk_size)
            if not data:
                break
            hash_obj.update(data)

        # Restore original position
        file_obj.seek(current_position)

        # Return hexadecimal digest
        return hash_obj.hexdigest()

    def mark_gdrive_file_processed(self, 
                                  file_id: str, 
                                  file_metadata: Dict[str, Any], 
                                  document_id: Optional[str] = None,
                                  status: str = "processed", 
                                  result: Optional[Dict[str, Any]] = None) -> bool:
        """
        Mark a Google Drive file as processed by adding it to the database.
        
        Args:
            file_id: Google Drive file ID
            file_metadata: File metadata from Google Drive
            document_id: Optional ID of the ingested document
            status: Processing status
            result: Optional processing result
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from sqlalchemy import text # Import text
            import json # Import json for dumps

            # Get the folder ID from file metadata
            folder_id = file_metadata.get('parents', [None])[0] if 'parents' in file_metadata else None
            
            # Create a SQL query for insert with ON CONFLICT DO UPDATE
            query = """
            INSERT INTO gdrive_processed_files 
            (id, file_id, file_name, mime_type, md5_checksum, folder_id, processed_at, document_id, status, result) 
            VALUES (gen_random_uuid(), :file_id, :file_name, :mime_type, :md5_checksum, :folder_id, CURRENT_TIMESTAMP, :document_id, :status, :result)
            ON CONFLICT (file_id) DO UPDATE SET
                file_name = EXCLUDED.file_name,
                mime_type = EXCLUDED.mime_type,
                md5_checksum = EXCLUDED.md5_checksum,
                folder_id = EXCLUDED.folder_id,
                processed_at = CURRENT_TIMESTAMP,
                document_id = EXCLUDED.document_id,
                status = EXCLUDED.status,
                result = EXCLUDED.result;
            """
            
            # Ensure result is JSON serializable string
            result_json = json.dumps(result) if result is not None else None

            self.db_client.db.execute(
                text(query),
                {
                    "file_id": file_id,
                    "file_name": file_metadata.get('name'),
                    "mime_type": file_metadata.get('mimeType'),
                    "md5_checksum": file_metadata.get('md5Checksum'),
                    "folder_id": folder_id,
                    "document_id": uuid.UUID(document_id) if document_id else None,
                    "status": status,
                    "result": result_json  # Pass JSON string
                }
             )
 
            self.db_client.db.commit()  # Commit the transaction through the session
 
            logger.info(f"File {file_id} marked as processed in database")
            return True
 
        except Exception as e:
            logger.error(f"Error marking file as processed: {str(e)}", exc_info=True)
            self.db_client.db.rollback()  # Rollback on error
            return False

    def is_gdrive_file_processed(self, file_id: str) -> bool:
        """
        Check if a Google Drive file has already been processed.

        Args:
            file_id: Google Drive file ID

        Returns:
            bool: True if file has been processed, False otherwise
        """
        try:
            from sqlalchemy import text # Import text
            # Execute a raw SQL query to check if the file exists
            query = text("SELECT COUNT(*) FROM gdrive_processed_files WHERE file_id = :file_id")

            result = self.db_client.db.execute(query, {"file_id": file_id})
            count = result.scalar_one()  # Get the single count value

            return count > 0

        except Exception as e:
            logger.error(f"Error checking if file is processed: {str(e)}", exc_info=True)
            return False

    def get_processed_gdrive_file_ids(self) -> List[str]:
        """
        Get a list of all processed Google Drive file IDs.

        Returns:
            List[str]: List of processed file IDs
        """
        try:
            from sqlalchemy import text # Import text
            # Execute a query to get all processed file IDs
            query = text("SELECT file_id FROM gdrive_processed_files")

            result = self.db_client.db.execute(query)
            processed_file_ids = [row[0] for row in result.fetchall()]

            return processed_file_ids

        except Exception as e:
            logger.error(f"Error getting processed file IDs: {str(e)}", exc_info=True)
            return []

    def get_gdrive_processed_file_stats(self) -> Dict[str, Any]:
        """
        Get statistics about processed Google Drive files.

        Returns:
            Dict[str, Any]: Statistics about processed files
        """
        try:
            from sqlalchemy import text # Import text
            # Get total count
            total_count_query = text("SELECT COUNT(*) FROM gdrive_processed_files")
            total_count = self.db_client.db.execute(total_count_query).scalar_one()

            # Get count by status
            status_counts_query = text("SELECT status, COUNT(*) FROM gdrive_processed_files GROUP BY status")
            status_counts_result = self.db_client.db.execute(status_counts_query).fetchall()
            status_counts = {status: count for status, count in status_counts_result}

            # Get recently processed files
            recent_files_query = text("""
                SELECT id, file_id, file_name, mime_type, status, processed_at, document_id
                FROM gdrive_processed_files
                ORDER BY processed_at DESC LIMIT 10
            """)
            recent_files_result = self.db_client.db.execute(recent_files_query).fetchall()

            recent_files = []
            for row in recent_files_result:
                recent_files.append({
                    "id": str(row[0]),
                    "file_id": row[1],
                    "file_name": row[2],
                    "mime_type": row[3],
                    "status": row[4],
                    "processed_at": row[5].isoformat() if row[5] else None,
                    "document_id": str(row[6]) if row[6] else None
                })

            return {
                "total_processed": total_count,
                "status_counts": status_counts,
                "recent_files": recent_files
            }

        except Exception as e:
            logger.error(f"Error getting processed file stats: {str(e)}", exc_info=True)
            return {
                "error": str(e)
            }

    def store_document(self,
                      file_obj: BinaryIO,
                      filename: str,
                      content_type: str,
                      source_path: str = "upload",
                      metadata: Optional[Dict[str, Any]] = None,
                      title: Optional[str] = None,
                      check_duplicates: bool = True) -> Dict[str, Any]:
        """
        Store a document in the system.

        Args:
            file_obj: File object
            filename: Original filename
            content_type: MIME type of the document
            source_path: Source path or URL
            metadata: Additional metadata
            title: Optional document title (if available)
            check_duplicates: Whether to check for duplicate documents

        Returns:
            Dict[str, Any]: Document information with is_duplicate flag
        """
        # Get file size
        file_obj.seek(0, os.SEEK_END)
        file_size = file_obj.tell()
        file_obj.seek(0)

        # Generate content hash
        content_hash = self._generate_document_hash(file_obj)

        # Check for duplicates if requested
        if check_duplicates:
            existing_document = self.db_client.get_document_by_hash(content_hash)
            if existing_document:
                logger.info(f"Duplicate document detected with hash {content_hash}")
                return {
                    "document_id": str(existing_document.id),
                    "filename": existing_document.filename,
                    "content_type": existing_document.content_type,
                    "storage_path": existing_document.storage_path,
                    "status": existing_document.status,
                    "is_duplicate": True
                }

        # Create initial document record
        document = self.db_client.create_document(
            filename=filename,
            source_path=source_path,
            content_type=content_type,
            file_size=file_size,
            metadata=metadata,
            title=title,
            content_hash=content_hash
        )

        # Upload file to storage
        storage_path = self.storage_client.upload_file(
            file_obj=file_obj,
            content_type=content_type,
            filename=filename
        )

        # Update document with storage path
        document = self.db_client.update_document_status(
            document_id=document.id,
            status="uploaded",
            storage_path=storage_path
        )

        return {
            "document_id": str(document.id),
            "filename": document.filename,
            "content_type": document.content_type,
            "storage_path": document.storage_path,
            "status": document.status,
            "is_duplicate": False
        }

    def store_document_from_url(self, url: str, metadata: Optional[Dict[str, Any]] = None, title: Optional[str] = None, check_duplicates: bool = True) -> Dict[str, Any]:
        """
        Store a document from a URL.

        Args:
            url: URL to download from
            metadata: Additional metadata
            title: Optional document title (if available)
            check_duplicates: Whether to check for duplicate documents

        Returns:
            Dict[str, Any]: Document information with is_duplicate flag
        """
        # Download file from URL
        content, content_type, filename = self.storage_client.download_from_url(url)

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Open file and store it
            with open(temp_file_path, 'rb') as file_obj:
                result = self.store_document(
                    file_obj=file_obj,
                    filename=filename,
                    content_type=content_type,
                    source_path=url,
                    metadata=metadata,
                    title=title,
                    check_duplicates=check_duplicates
                )

            return result
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    def get_document_content(self, document_id: Union[str, uuid.UUID]) -> Tuple[bytes, Dict[str, Any]]:
        """
        Get document content and metadata.

        Args:
            document_id: Document ID

        Returns:
            Tuple[bytes, Dict[str, Any]]: Document content and metadata
        """
        # Get document record
        document = self.db_client.get_document(document_id)
        if not document:
            raise ValueError(f"Document with ID {document_id} not found")

        if not document.storage_path:
            raise ValueError(f"Document with ID {document_id} has no storage path")

        # Download file from storage
        content, storage_metadata = self.storage_client.download_file(document.storage_path)

        # Combine database and storage metadata
        metadata = {
            "document_id": str(document.id),
            "filename": document.filename,
            "content_type": document.content_type,
            "file_size": document.file_size,
            "status": document.status,
            "created_at": document.created_at.isoformat() if document.created_at else None,
            "updated_at": document.updated_at.isoformat() if document.updated_at else None,
            "storage_metadata": storage_metadata,
            "metadata": document.metadata or {}
        }

        return content, metadata

    def get_document_download_url(self, document_id: Union[str, uuid.UUID], expires: int = 3600) -> str:
        """
        Get presigned URL for downloading a document.

        Args:
            document_id: Document ID
            expires: Expiration time in seconds

        Returns:
            str: Presigned URL
        """
        # Get document record
        document = self.db_client.get_document(document_id)
        if not document:
            raise ValueError(f"Document with ID {document_id} not found")

        if not document.storage_path:
            raise ValueError(f"Document with ID {document_id} has no storage path")

        # Generate presigned URL
        return self.storage_client.get_presigned_url(document.storage_path, expires)

    def store_document_chunks(self,
                             document_id: Union[str, uuid.UUID],
                             chunks: List[Dict[str, Any]],
                             chunk_type: str = "rag") -> Dict[str, Any]:
        """
        Store document chunks in the database.

        Args:
            document_id: Document ID
            chunks: List of chunk objects with text, metadata, and optional embedding
            chunk_type: Type of chunks to store ("rag" or "kg")

        Returns:
            Dict[str, Any]: Result information
        """
        # Store chunks
        chunk_records = self.db_client.store_chunks(document_id, chunks, chunk_type)

        return {
            "document_id": str(document_id),
            "chunks_count": len(chunk_records),
            "chunk_type": chunk_type,
            "status": "success"
        }

    def search_similar_chunks(self, embedding: List[float], limit: int = 10, exclude_chunk_id: Optional[Union[str, uuid.UUID]] = None,
                              min_similarity: float = 0.0, filter_document_id: Optional[Union[str, uuid.UUID]] = None) -> List[Dict[str, Any]]:
        """
        Search for similar chunks using vector similarity with pgvector.

        Args:
            embedding: Query embedding vector
            limit: Maximum number of results
            exclude_chunk_id: Optional chunk ID to exclude from results
            min_similarity: Minimum similarity threshold (0.0 to 1.0)
            filter_document_id: Optional document ID to filter results by

        Returns:
            List[Dict[str, Any]]: List of similar chunks with similarity scores
        """
        return self.db_client.search_similar(embedding, limit, exclude_chunk_id, min_similarity, filter_document_id)

    def find_similar_chunks_by_id(self, chunk_id: Union[str, uuid.UUID], limit: int = 10,
                                 min_similarity: float = 0.0, filter_document_id: Optional[Union[str, uuid.UUID]] = None) -> List[Dict[str, Any]]:
        """
        Find chunks similar to a specific chunk using pgvector similarity search.

        This method is useful for finding related content to a specific chunk
        without needing to generate a new embedding.

        Args:
            chunk_id: ID of the chunk to find similar chunks for
            limit: Maximum number of results
            min_similarity: Minimum similarity threshold (0.0 to 1.0)
            filter_document_id: Optional document ID to filter results by

        Returns:
            List[Dict[str, Any]]: List of similar chunks with similarity scores
        """
        return self.db_client.find_similar_by_chunk_id(chunk_id, limit, min_similarity, filter_document_id)

    def get_document_chunks(self, document_id: Union[str, uuid.UUID], chunk_type: str = "rag") -> List[Dict[str, Any]]:
        """
        Get all chunks for a document.

        Args:
            document_id: Document ID
            chunk_type: Type of chunks to retrieve ("rag" or "kg")

        Returns:
            List[Dict[str, Any]]: List of document chunks
        """
        chunks = self.db_client.get_document_chunks(document_id, chunk_type)
        return [chunk.to_dict() for chunk in chunks]

    def get_chunk(self, chunk_id: Union[str, uuid.UUID], chunk_type: str = "rag") -> Optional[Dict[str, Any]]:
        """
        Get a specific chunk by ID.

        Args:
            chunk_id: Chunk ID
            chunk_type: Type of chunk to retrieve ("rag" or "kg")

        Returns:
            Optional[Dict[str, Any]]: Chunk data if found, None otherwise
        """
        chunk = self.db_client.get_chunk(chunk_id, chunk_type)
        return chunk.to_dict() if chunk else None

    def store_entities(self, document_id: Union[str, uuid.UUID], entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Store entities extracted from a document.

        Args:
            document_id: Document ID
            entities: List of entity objects

        Returns:
            Dict[str, Any]: Result information
        """
        # For now, just log the entities - in a real implementation, we would store them in the database
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Storing {len(entities)} entities for document {document_id}")

        return {
            "document_id": str(document_id),
            "entities_count": len(entities),
            "status": "success"
        }

    def store_relationships(self, document_id: Union[str, uuid.UUID], relationships: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Store relationships extracted from a document.

        Args:
            document_id: Document ID
            relationships: List of relationship objects

        Returns:
            Dict[str, Any]]: Result information
        """
        # For now, just log the relationships - in a real implementation, we would store them in the database
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Storing {len(relationships)} relationships for document {document_id}")

        return {
            "document_id": str(document_id),
            "relationships_count": len(relationships),
            "status": "success"
        }

    def get_document_entities(self, document_id: Union[str, uuid.UUID]) -> List[Dict[str, Any]]:
        """
        Get entities extracted from a document.

        Args:
            document_id: Document ID

        Returns:
            List[Dict[str, Any]]: List of entities
        """
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Getting entities for document {document_id}")

        # Query entities from database
        entities = self.db_client.get_entities_by_document(document_id)

        # Convert to dictionary format
        result = []
        for entity in entities:
            result.append({
                "id": str(entity.id),
                "document_id": str(entity.document_id),
                "chunk_id": str(entity.chunk_id) if entity.chunk_id else None,
                "text": entity.text,
                "type": entity.entity_type,
                "normalized_id": entity.normalized_id,
                "normalized_text": entity.entity_metadata.get("normalized_text", entity.text) if entity.entity_metadata else entity.text,
                "start": entity.start_pos,
                "end": entity.end_pos,
                "is_duplicate": entity.entity_metadata.get("is_duplicate", False) if entity.entity_metadata else False,
                "embedding": entity.entity_metadata.get("embedding") if entity.entity_metadata else None
            })

        return result

    def find_similar_entities(self, text: str, embedding: Optional[List[float]] = None,
                             entity_type: Optional[str] = None, threshold: float = 0.9,
                             limit: int = 5) -> List[Dict[str, Any]]:
        """
        Find entities similar to the given text using vector similarity.

        Args:
            text: Entity text to search for
            embedding: Optional pre-computed embedding for the text
            entity_type: Optional entity type to filter by
            threshold: Similarity threshold (0-1)
            limit: Maximum number of results to return

        Returns:
            List[Dict[str, Any]]: List of similar entities
        """
        import logging
        logger = logging.getLogger(__name__)

        # Generate embedding if not provided
        if embedding is None:
            from services.nlp_service import NLPService
            embedding = NLPService.generate_embeddings([text])[0]

        # Query similar entities from database
        similar_entities = self.db_client.find_similar_entities(
            embedding=embedding,
            entity_type=entity_type,
            threshold=threshold,
            limit=limit
        )

        # Convert to dictionary format
        result = []
        for entity, similarity in similar_entities:
            result.append({
                "id": str(entity.id),
                "document_id": str(entity.document_id),
                "chunk_id": str(entity.chunk_id) if entity.chunk_id else None,
                "text": entity.text,
                "type": entity.entity_type,
                "normalized_id": entity.normalized_id,
                "normalized_text": entity.entity_metadata.get("normalized_text", entity.text) if entity.entity_metadata else entity.text,
                "similarity": float(similarity),
                "embedding": entity.entity_metadata.get("embedding") if entity.entity_metadata else None
            })

        logger.info(f"Found {len(result)} similar entities for '{text}'")
        return result

    def get_document_relationships(self, document_id: Union[str, uuid.UUID]) -> List[Dict[str, Any]]:
        """
        Get relationships extracted from a document.

        Args:
            document_id: Document ID

        Returns:
            List[Dict[str, Any]]: List of relationships
        """
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Getting relationships for document {document_id}")

        # Query Neo4j for relationships
        # TODO: Implement this method in Neo4jClient
        # return self.neo4j_client.get_document_relationships(document_id)
        return []

    def get_chunks_mentioning_entity(self, entity_id: Union[str, uuid.UUID], limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get all chunks that mention a specific entity.

        Args:
            entity_id: Entity ID
            limit: Maximum number of results

        Returns:
            List[Dict[str, Any]]: List of chunks mentioning the entity
        """
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Getting chunks mentioning entity {entity_id}")

        # Convert entity_id to string if it's a UUID
        if isinstance(entity_id, uuid.UUID):
            entity_id = str(entity_id)

        # Query Neo4j for chunks mentioning the entity
        return self.neo4j_client.get_chunks_mentioning_entity(entity_id, limit)

    def create_entity(self, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an entity in Neo4j.

        Args:
            entity_data: Entity data including chunk_id, text, entity_type, etc.

        Returns:
            Dict[str, Any]: Result information
        """
        import logging
        logger = logging.getLogger(__name__)

        chunk_id = entity_data.get("chunk_id")
        if not chunk_id:
            raise ValueError("chunk_id is required for creating an entity")

        # Create entity object for Neo4j
        entity = {
            "id": str(uuid.uuid4()),
            "text": entity_data.get("text", ""),
            "type": entity_data.get("entity_type", ""),
            "start": entity_data.get("start_pos", 0),
            "end": entity_data.get("end_pos", 0),
            "normalized_id": None  # Could be added if available
        }

        # Store entity in Neo4j
        result = self.neo4j_client.store_entities(chunk_id, [entity])

        logger.info(f"Created entity in Neo4j: {entity['text']} ({entity['type']}) in chunk {chunk_id}")

        return {
            "entity_id": entity["id"],
            "chunk_id": chunk_id,
            "status": result.get("status", "error")
        }

    def create_relationship(self, relationship_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a relationship in Neo4j.

        Args:
            relationship_data: Relationship data including chunk_id, source_text, target_text, etc.

        Returns:
            Dict[str, Any]: Result information
        """
        import logging
        logger = logging.getLogger(__name__)

        chunk_id = relationship_data.get("chunk_id")
        if not chunk_id:
            raise ValueError("chunk_id is required for creating a relationship")

        # Find or create source and target entities
        source_entity = {
            "id": str(uuid.uuid4()),
            "text": relationship_data.get("source_text", ""),
            "type": relationship_data.get("source_type", ""),
            "start": 0,  # Default values since we don't have position info
            "end": 0
        }

        target_entity = {
            "id": str(uuid.uuid4()),
            "text": relationship_data.get("target_text", ""),
            "type": relationship_data.get("target_type", ""),
            "start": 0,  # Default values since we don't have position info
            "end": 0
        }

        # Store entities in Neo4j to ensure they exist
        self.neo4j_client.store_entities(chunk_id, [source_entity, target_entity])

        # Create relationship object for Neo4j
        relationship = {
            "id": str(uuid.uuid4()),
            "source_id": source_entity["id"],
            "target_id": target_entity["id"],
            "relationship_type": relationship_data.get("relationship_type", "RELATED_TO"),
            "evidence_text": relationship_data.get("evidence_text", ""),
            "confidence": 1.0  # Default confidence
        }

        # Store relationship in Neo4j
        result = self.neo4j_client.store_relationships(chunk_id, [relationship])

        logger.info(f"Created relationship in Neo4j: {source_entity['text']} --[{relationship['relationship_type']}]--> {target_entity['text']} in chunk {chunk_id}")

        return {
            "relationship_id": relationship["id"],
            "source_id": source_entity["id"],
            "target_id": target_entity["id"],
            "chunk_id": chunk_id,
            "status": result.get("status", "error")
        }

    def create_processing_task(self,
                              document_id: Union[str, uuid.UUID],
                              task_type: str,
                              celery_task_id: Optional[str] = None,
                              metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a processing task record.

        Args:
            document_id: Document ID
            task_type: Type of processing task
            celery_task_id: Celery task ID if available
            metadata: Additional metadata for the task

        Returns:
            Dict[str, Any]: Task information
        """
        # Create initial task record
        task = self.db_client.create_processing_task(
            document_id=document_id,
            task_type=task_type,
            celery_task_id=celery_task_id
        )

        # Update task with metadata if provided
        if metadata:
            task = self.db_client.update_task_status(
                task_id=task.id,
                status=task.status,
                result=metadata
            )

        return {
            "task_id": str(task.id),
            "document_id": str(task.document_id),
            "task_type": task.task_type,
            "celery_task_id": task.celery_task_id,
            "status": task.status
        }

    def update_task_status(self,
                          task_id: Union[str, uuid.UUID],
                          status: str,
                          result: Optional[Dict[str, Any]] = None,
                          error: Optional[str] = None) -> Dict[str, Any]:
        """
        Update task status.

        Args:
            task_id: Task ID
            status: New status
            result: Task result (for completed tasks)
            error: Error message (for failed tasks)

        Returns:
            Dict[str, Any]: Updated task information
        """
        task = self.db_client.update_task_status(
            task_id=task_id,
            status=status,
            result=result,
            error=error
        )

        return task

    def get_processing_task(self, task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Get processing task by ID.

        Args:
            task_id: Task ID

        Returns:
            Dict[str, Any]: Task information
        """
        task = self.db_client.get_processing_task(task_id)
        if not task:
            raise ValueError(f"Task with ID {task_id} not found")

        return task.to_dict()

    def get_processing_tasks_by_document(self, document_id: Union[str, uuid.UUID]) -> List[Dict[str, Any]]:
        """
        Get all processing tasks for a document.

        Args:
            document_id: Document ID

        Returns:
            List[Dict[str, Any]]: List of task information
        """
        tasks = self.db_client.get_processing_tasks_by_document(document_id)
        return [task.to_dict() for task in tasks]

    def get_processing_tasks_by_celery_id(self, celery_task_id: str) -> List[Dict[str, Any]]:
        """
        Get all processing tasks with a specific Celery task ID.

        Args:
            celery_task_id: Celery task ID

        Returns:
            List[Dict[str, Any]]: List of task information
        """
        tasks = self.db_client.get_processing_tasks_by_celery_id(celery_task_id)
        return [task.to_dict() for task in tasks]

    def verify_document_chunk_relationships(self, document_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Verify relationships between a document and its chunks in Neo4j.

        Args:
            document_id: Document ID

        Returns:
            Dict[str, Any]: Verification results
        """
        if isinstance(document_id, uuid.UUID):
            document_id = str(document_id)

        return self.neo4j_client.verify_document_chunk_relationships(document_id)

    def fix_document_chunk_relationships(self, document_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Fix relationships between a document and its chunks in Neo4j.

        Args:
            document_id: Document ID

        Returns:
            Dict[str, Any]: Fix results
        """
        if isinstance(document_id, uuid.UUID):
            document_id = str(document_id)

        # First verify relationships
        verification = self.neo4j_client.verify_document_chunk_relationships(document_id)
        logger.info(f"Document {document_id} verification before fixing:")
        logger.info(f"  Document exists: {verification['document_exists']}")
        logger.info(f"  Chunk count: {verification['chunk_count']}")

        # Fix orphaned chunks
        fix_result = self.neo4j_client.fix_orphaned_chunks(document_id)
        logger.info(f"Fixed {fix_result['chunks_fixed']} orphaned chunks for document {document_id}")

        # Verify again after fixing
        verification_after = self.neo4j_client.verify_document_chunk_relationships(document_id)
        logger.info(f"Document {document_id} verification after fixing:")
        logger.info(f"  Document exists: {verification_after['document_exists']}")
        logger.info(f"  Chunk count: {verification_after['chunk_count']}")

        # Return combined results
        return {
            "document_id": document_id,
            "before": verification,
            "after": verification_after,
            "fixed": fix_result
        }

    def store_pubmed_article(self,
                             pmid: str,
                             title: str,
                             abstract: str,
                             document_id: Union[str, uuid.UUID],
                             authors: Optional[Union[List[Dict], str]] = None,
                             journal: Optional[str] = None,
                             publication_date: Optional[Union[str, Any]] = None,
                             publication_year: Optional[int] = None,
                             mesh_terms: Optional[List[str]] = None,
                             keywords: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Store PubMed article details in the pubmed_articles table.
        
        Args:
            pmid: PubMed ID
            title: Article title
            abstract: Article abstract
            document_id: Associated document ID in the documents table
            authors: List of authors or JSON string of authors
            journal: Journal name
            publication_date: Publication date (string or datetime object)
            publication_year: Publication year as integer
            mesh_terms: List of MeSH terms
            keywords: List of keywords

        Returns:
            Dict[str, Any]: Result information
        """
        import json
        from datetime import datetime
        from sqlalchemy import text
        
        logger.info(f"Storing PubMed article details for PMID {pmid}")
        
        # Convert document_id to string if it's a UUID
        if isinstance(document_id, uuid.UUID):
            document_id = str(document_id)
            
        # Process publication date and year
        pub_date_obj = None
        if publication_date:
            if isinstance(publication_date, str):
                try:
                    pub_date_obj = datetime.fromisoformat(publication_date.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    logger.warning(f"Could not parse publication date: {publication_date}")
            else:
                pub_date_obj = publication_date
                
        # If year isn't provided but date is, extract year
        if publication_year is None and pub_date_obj:
            publication_year = pub_date_obj.year
        elif publication_year is None and isinstance(publication_date, str):
            # Try to extract year from ISO format date string
            try:
                publication_year = int(publication_date.split('-')[0])
            except (IndexError, ValueError):
                logger.warning(f"Could not parse publication year from date: {publication_date}")
                
        # Ensure authors, mesh_terms, and keywords are JSON serializable
        if authors is not None and not isinstance(authors, str):
            authors_json = json.dumps(authors)
        else:
            authors_json = authors or json.dumps([])
            
        mesh_terms_json = json.dumps(mesh_terms) if mesh_terms else json.dumps([])
        keywords_json = json.dumps(keywords) if keywords else json.dumps([])
        
        # Execute SQL to insert into pubmed_articles table using SQLAlchemy ORM approach
        query = text("""
        INSERT INTO pubmed_articles 
        (pmid, title, abstract, authors, journal, publication_date, publication_year, 
         mesh_terms, keywords, document_id) 
        VALUES (:pmid, :title, :abstract, :authors, :journal, :pub_date, :pub_year, :mesh_terms, :keywords, :doc_id)
        ON CONFLICT (pmid) DO UPDATE SET
        title = EXCLUDED.title,
        abstract = EXCLUDED.abstract,
        authors = EXCLUDED.authors,
        journal = EXCLUDED.journal,
        publication_date = EXCLUDED.publication_date,
        publication_year = EXCLUDED.publication_year,
        mesh_terms = EXCLUDED.mesh_terms,
        keywords = EXCLUDED.keywords,
        document_id = EXCLUDED.document_id,
        updated_at = CURRENT_TIMESTAMP;
        """)
        
        try:
            # Execute the query using SQLAlchemy session
            self.db_client.db.execute(
                query, 
                {
                    "pmid": pmid, 
                    "title": title, 
                    "abstract": abstract, 
                    "authors": authors_json, 
                    "journal": journal,
                    "pub_date": pub_date_obj, 
                    "pub_year": publication_year, 
                    "mesh_terms": mesh_terms_json, 
                    "keywords": keywords_json, 
                    "doc_id": document_id
                }
            )
            self.db_client.db.commit()
            
            logger.info(f"Successfully stored article with PMID {pmid} in pubmed_articles table")
            
            return {
                "status": "success",
                "pmid": pmid,
                "document_id": document_id
            }
            
        except Exception as e:
            logger.error(f"Error storing PubMed article with PMID {pmid}: {str(e)}")
            self.db_client.db.rollback()
            # Return error info
            return {
                "status": "error",
                "pmid": pmid,
                "error": str(e)
            }

    def get_stale_tasks(self, task_types: List[str], statuses: List[str], hours_threshold: int) -> List[Dict[str, Any]]:
        logger.info(f"Fetching stale tasks older than {hours_threshold} hours for types {task_types} and statuses {statuses}")
        try:
            threshold_time = datetime.utcnow() - timedelta(hours=hours_threshold)

            query = self.db_client.db.query(ProcessingTask).filter(
                ProcessingTask.task_type.in_(task_types),
                ProcessingTask.status.in_(statuses),
                ProcessingTask.updated_at < threshold_time
            )

            stale_task_objects = query.all()

            stale_tasks_list = []
            for task in stale_task_objects:
                stale_tasks_list.append({
                    "id": str(task.id),
                    "task_type": task.task_type,
                    "status": task.status,
                    "result": task.result,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None
                })

            logger.info(f"Found {len(stale_tasks_list)} stale tasks.")
            return stale_tasks_list

        except Exception as e:
            logger.error(f"Error fetching stale tasks: {e}", exc_info=True)
            return []

    def get_pending_vertex_ai_batch_jobs(self, max_tasks: int = 10) -> List[Dict[str, Any]]:
        """
        Get pending vertex_ai_batch_job tasks that need status checking.

        Args:
            max_tasks: Maximum number of tasks to return

        Returns:
            List[Dict[str, Any]]: List of pending batch job tasks
        """
        logger.info(f"Fetching pending vertex_ai_batch_job tasks (max: {max_tasks})")
        try:
            from common.task_definitions import TaskStatus, TaskType

            # Query for vertex_ai_batch_job tasks in created or processing status
            query = self.db_client.db.query(ProcessingTask).filter(
                ProcessingTask.task_type == TaskType.VERTEX_AI_BATCH_JOB.value,
                ProcessingTask.status.in_([
                    TaskStatus.CREATED.value,
                    TaskStatus.PROCESSING.value
                ])
            ).order_by(ProcessingTask.created_at.asc()).limit(max_tasks)

            pending_tasks = query.all()

            pending_jobs_list = []
            for task in pending_tasks:
                # Extract batch_job_id from metadata or result
                batch_job_id = None
                if task.task_metadata and "batch_job_id" in task.task_metadata:
                    batch_job_id = task.task_metadata["batch_job_id"]
                elif task.result and "batch_job_id" in task.result:
                    batch_job_id = task.result["batch_job_id"]

                # Only include tasks that have a batch_job_id
                if batch_job_id:
                    pending_jobs_list.append({
                        "id": str(task.id),
                        "task_type": task.task_type,
                        "status": task.status,
                        "batch_job_id": batch_job_id,
                        "result": task.result,
                        "metadata": task.task_metadata,
                        "created_at": task.created_at.isoformat() if task.created_at else None,
                        "updated_at": task.updated_at.isoformat() if task.updated_at else None
                    })

            logger.info(f"Found {len(pending_jobs_list)} pending vertex_ai_batch_job tasks.")
            return pending_jobs_list

        except Exception as e:
            logger.error(f"Error fetching pending vertex_ai_batch_job tasks: {e}", exc_info=True)
            return []

    def get_task_status_summary(self) -> Dict[str, Any]:
        """
        Get a summary of task statuses across all task types.

        Returns:
            Dict[str, Any]: Summary of task statuses
        """
        logger.info("Fetching task status summary")
        try:
            from sqlalchemy import text

            # Get total count of tasks
            total_count_query = text("SELECT COUNT(*) FROM processing_tasks")
            total_count = self.db_client.db.execute(total_count_query).scalar_one()

            # Get count by status
            status_counts_query = text("SELECT status, COUNT(*) FROM processing_tasks GROUP BY status")
            status_counts_result = self.db_client.db.execute(status_counts_query).fetchall()
            status_counts = {status: count for status, count in status_counts_result}

            # Get count by task type
            task_type_counts_query = text("SELECT task_type, COUNT(*) FROM processing_tasks GROUP BY task_type")
            task_type_counts_result = self.db_client.db.execute(task_type_counts_query).fetchall()
            task_type_counts = {task_type: count for task_type, count in task_type_counts_result}

            # Get recent tasks
            recent_tasks_query = text("""
                SELECT id, task_type, status, created_at, updated_at
                FROM processing_tasks
                ORDER BY updated_at DESC LIMIT 10
            """)
            recent_tasks_result = self.db_client.db.execute(recent_tasks_query).fetchall()

            recent_tasks = []
            for row in recent_tasks_result:
                recent_tasks.append({
                    "id": str(row[0]),
                    "task_type": row[1],
                    "status": row[2],
                    "created_at": row[3].isoformat() if row[3] else None,
                    "updated_at": row[4].isoformat() if row[4] else None
                })

            summary = {
                "total_tasks": total_count,
                "status_counts": status_counts,
                "task_type_counts": task_type_counts,
                "recent_tasks": recent_tasks
            }

            logger.info(f"Task status summary: {total_count} total tasks, {len(status_counts)} statuses, {len(task_type_counts)} task types")
            return summary

        except Exception as e:
            logger.error(f"Error fetching task status summary: {e}", exc_info=True)
            return {
                "total_tasks": 0,
                "status_counts": {},
                "task_type_counts": {},
                "recent_tasks": []
            }