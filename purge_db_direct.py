#!/usr/bin/env python3
"""
Script to purge all data from PostgreSQL and Neo4j databases directly.
"""
import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from neo4j import GraphDatabase

# Import settings
from common.config import settings

def purge_postgres_direct():
    """Purge all data from PostgreSQL directly."""
    print("Purging PostgreSQL database...")
    
    # Override settings for local testing
    settings.POSTGRES_HOST = "localhost"
    settings.POSTGRES_PORT = 5435  # Mapped port from docker-compose
    
    # Update database URL
    settings.DATABASE_URL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
    
    print(f"Connecting to: {settings.DATABASE_URL}")
    
    try:
        # Create engine and connect
        engine = create_engine(settings.DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db_session = SessionLocal()
        
        # Delete all data in reverse order of dependencies
        print("Deleting relationships...")
        db_session.execute(text("DELETE FROM relationships"))
        
        print("Deleting entities...")
        db_session.execute(text("DELETE FROM entities"))
        
        print("Deleting KG chunks...")
        db_session.execute(text("DELETE FROM kg_chunks"))
        
        print("Deleting chunks...")
        db_session.execute(text("DELETE FROM chunks"))
        
        print("Deleting processing tasks...")
        db_session.execute(text("DELETE FROM processing_tasks"))
        
        print("Deleting documents...")
        db_session.execute(text("DELETE FROM documents"))
        
        db_session.commit()
        print("PostgreSQL database purged successfully.")
        return True
    except Exception as e:
        print(f"Error purging PostgreSQL database: {e}")
        return False
    finally:
        if 'db_session' in locals():
            db_session.close()

def purge_neo4j_direct():
    """Purge all data from Neo4j directly."""
    print("Purging Neo4j database...")
    
    # Override settings for local testing
    settings.NEO4J_HOST = "localhost"
    settings.NEO4J_PORT = 7687  # Default Neo4j Bolt port
    
    # Update Neo4j URI
    settings.NEO4J_URI = f"bolt://{settings.NEO4J_HOST}:{settings.NEO4J_PORT}"
    
    print(f"Connecting to: {settings.NEO4J_URI}")
    
    try:
        # Connect to Neo4j
        driver = GraphDatabase.driver(
            settings.NEO4J_URI,
            auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
        )
        
        with driver.session() as session:
            # Delete all nodes and relationships
            session.run("MATCH (n) DETACH DELETE n")
        
        print("Neo4j database purged successfully.")
        driver.close()
        return True
    except Exception as e:
        print(f"Error purging Neo4j database: {e}")
        return False

def main():
    """Main function."""
    print("Purging databases directly...")
    
    postgres_ok = purge_postgres_direct()
    neo4j_ok = purge_neo4j_direct()
    
    if postgres_ok and neo4j_ok:
        print("All databases purged successfully.")
        return 0
    else:
        print("Some database purges failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
