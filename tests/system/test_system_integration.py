#!/usr/bin/env python3
"""
Comprehensive system integration tests for the LongevityCo platform.
Tests the complete document ingestion pipeline from API to database storage.
"""

import os
import sys
import pytest
import time
import requests
import json
from typing import Dict, Any, List
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from common.config import settings
from common.database import get_db
from services.neo4j_service import Neo4jService
from transport.data_transport import DataTransport
from sqlalchemy import text


class TestSystemIntegration:
    """System integration tests for document ingestion pipeline."""
    
    @classmethod
    def setup_class(cls):
        """Set up test environment."""
        cls.api_base_url = f"http://localhost:{settings.API_PORT}"
        cls.test_data_dir = Path(__file__).parent.parent / "data"
        
        # Verify API is running
        cls._wait_for_api()
        
        # Clean databases before testing
        cls._cleanup_databases()
    
    @classmethod
    def _wait_for_api(cls, timeout: int = 60):
        """Wait for API to be available."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{cls.api_base_url}/health", timeout=5)
                if response.status_code == 200:
                    print("API is ready")
                    return
            except requests.exceptions.RequestException:
                pass
            time.sleep(2)
        
        raise Exception(f"API not available after {timeout} seconds")
    
    @classmethod
    def _cleanup_databases(cls):
        """Clean all databases before testing."""
        print("Cleaning databases...")
        
        # Clean PostgreSQL
        try:
            db = next(get_db())
            tables = ['processing_tasks', 'kg_chunks', 'rag_chunks', 'documents', 'gdrive_processed_files']
            for table in tables:
                try:
                    db.execute(text(f"DELETE FROM {table}"))
                except Exception as e:
                    print(f"Warning: Could not clean table {table}: {e}")
            db.commit()
            db.close()
            print("PostgreSQL cleaned")
        except Exception as e:
            print(f"Warning: PostgreSQL cleanup failed: {e}")
        
        # Clean Neo4j
        try:
            neo4j_service = Neo4jService()
            with neo4j_service.driver.session() as session:
                session.run("MATCH ()-[r]->() DELETE r")
                session.run("MATCH (n) DELETE n")
            neo4j_service.close()
            print("Neo4j cleaned")
        except Exception as e:
            print(f"Warning: Neo4j cleanup failed: {e}")
    
    def test_api_health_check(self):
        """Test that API health check endpoint works."""
        response = requests.get(f"{self.api_base_url}/health")
        assert response.status_code == 200
        
        health_data = response.json()
        assert health_data["status"] == "healthy"
    
    def test_file_upload_ingestion(self):
        """Test file upload ingestion pipeline."""
        # Test with sample text file
        test_file = self.test_data_dir / "sample_txt.txt"
        if not test_file.exists():
            pytest.skip(f"Test file {test_file} not found")
        
        # Upload file
        with open(test_file, 'rb') as f:
            files = {'file': ('sample_txt.txt', f, 'text/plain')}
            data = {
                'build_knowledge_graph': 'true',
                'chunking_strategy': 'standard'
            }
            
            response = requests.post(
                f"{self.api_base_url}/ingest/upload",
                files=files,
                data=data
            )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "document_id" in result
        assert "task_id" in result
        
        document_id = result["document_id"]
        
        # Wait for processing to complete
        self._wait_for_document_processing(document_id)
        
        # Verify document was created
        self._verify_document_in_database(document_id)
        
        # Verify RAG chunks were created
        self._verify_rag_chunks(document_id)
        
        return document_id
    
    def test_pubmed_ingestion(self):
        """Test PubMed article ingestion."""
        # Ingest PubMed articles with "longevity diet" query
        data = {
            'query': 'longevity diet',
            'max_results': 2,
            'build_knowledge_graph': True
        }
        
        response = requests.post(
            f"{self.api_base_url}/ingest/pubmed",
            json=data
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "task_id" in result
        assert "message" in result
        
        # Wait for processing to complete
        time.sleep(10)  # Give some time for PubMed processing
        
        # Verify documents were created
        documents = self._get_documents_from_database()
        pubmed_docs = [doc for doc in documents if doc.get('source', {}).get('type') == 'pubmed']
        
        assert len(pubmed_docs) > 0, "No PubMed documents found"
        
        # Verify at least one document has KG chunks
        for doc in pubmed_docs[:1]:  # Check first document
            self._verify_kg_chunks(doc['id'])
    
    def test_url_ingestion(self):
        """Test URL ingestion."""
        # Read URL from sample_url.txt
        url_file = self.test_data_dir / "sample_url.txt"
        if not url_file.exists():
            pytest.skip(f"URL file {url_file} not found")
        
        with open(url_file, 'r') as f:
            url = f.read().strip()
        
        data = {
            'url': url,
            'build_knowledge_graph': True
        }
        
        response = requests.post(
            f"{self.api_base_url}/ingest/url",
            json=data
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "document_id" in result
        document_id = result["document_id"]
        
        # Wait for processing
        self._wait_for_document_processing(document_id)
        
        # Verify document and chunks
        self._verify_document_in_database(document_id)
        self._verify_rag_chunks(document_id)
    
    def _wait_for_document_processing(self, document_id: str, timeout: int = 300):
        """Wait for document processing to complete."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.api_base_url}/documents/{document_id}")
                if response.status_code == 200:
                    doc_data = response.json()
                    if doc_data.get("status") in ["processed", "completed"]:
                        return
            except Exception as e:
                print(f"Error checking document status: {e}")
            
            time.sleep(5)
        
        raise Exception(f"Document {document_id} processing timeout after {timeout} seconds")
    
    def _verify_document_in_database(self, document_id: str):
        """Verify document exists in database."""
        db = next(get_db())
        try:
            result = db.execute(text("SELECT * FROM documents WHERE id = :doc_id"), {"doc_id": document_id})
            document = result.fetchone()
            assert document is not None, f"Document {document_id} not found in database"
        finally:
            db.close()
    
    def _verify_rag_chunks(self, document_id: str):
        """Verify RAG chunks were created for document."""
        db = next(get_db())
        try:
            result = db.execute(text("SELECT COUNT(*) FROM rag_chunks WHERE document_id = :doc_id"), {"doc_id": document_id})
            count = result.scalar()
            assert count > 0, f"No RAG chunks found for document {document_id}"
            print(f"Found {count} RAG chunks for document {document_id}")
        finally:
            db.close()
    
    def _verify_kg_chunks(self, document_id: str):
        """Verify KG chunks were created for document."""
        db = next(get_db())
        try:
            result = db.execute(text("SELECT COUNT(*) FROM kg_chunks WHERE document_id = :doc_id"), {"doc_id": document_id})
            count = result.scalar()
            assert count > 0, f"No KG chunks found for document {document_id}"
            print(f"Found {count} KG chunks for document {document_id}")
        finally:
            db.close()
    
    def _get_documents_from_database(self) -> List[Dict[str, Any]]:
        """Get all documents from database."""
        db = next(get_db())
        try:
            result = db.execute(text("SELECT id, title, metadata FROM documents"))
            documents = []
            for row in result.fetchall():
                documents.append({
                    'id': str(row[0]),
                    'title': row[1],
                    'metadata': row[2] if row[2] else {}
                })
            return documents
        finally:
            db.close()


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v", "-s"])
