#!/usr/bin/env python3
"""
Document ingestion pipeline tests.
Tests the complete document processing workflow including batch processing.
"""

import os
import sys
import pytest
import time
import requests
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from common.config import settings
from common.database import get_db
from services.neo4j_service import Neo4jService
from transport.data_transport import DataTransport
from sqlalchemy import text


class TestDocumentIngestion:
    """Test document ingestion pipeline with various file types."""
    
    @classmethod
    def setup_class(cls):
        """Set up test environment."""
        cls.api_base_url = f"http://localhost:{settings.API_PORT}"
        cls.test_data_dir = Path(__file__).parent.parent / "data"
        cls.processed_documents = []
        
        # Verify test data exists
        cls._verify_test_data()
    
    @classmethod
    def _verify_test_data(cls):
        """Verify test data files exist."""
        required_files = [
            "sample_txt.txt",
            "sample_pdf.pdf", 
            "sample_epub.epub",
            "sample_url.txt"
        ]
        
        missing_files = []
        for file_name in required_files:
            file_path = cls.test_data_dir / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            print(f"Warning: Missing test files: {missing_files}")
    
    def test_text_file_ingestion(self):
        """Test text file ingestion with knowledge graph building."""
        test_file = self.test_data_dir / "sample_txt.txt"
        if not test_file.exists():
            pytest.skip(f"Test file {test_file} not found")
        
        document_id = self._ingest_file(
            file_path=test_file,
            content_type='text/plain',
            build_kg=True
        )
        
        # Verify processing
        self._wait_for_processing(document_id)
        self._verify_document_processing(document_id, expect_kg=True)
        
        self.processed_documents.append(document_id)
    
    def test_pdf_file_ingestion(self):
        """Test PDF file ingestion."""
        test_file = self.test_data_dir / "sample_pdf.pdf"
        if not test_file.exists():
            pytest.skip(f"Test file {test_file} not found")
        
        document_id = self._ingest_file(
            file_path=test_file,
            content_type='application/pdf',
            build_kg=True
        )
        
        # Verify processing
        self._wait_for_processing(document_id)
        self._verify_document_processing(document_id, expect_kg=True)
        
        self.processed_documents.append(document_id)
    
    def test_epub_file_ingestion(self):
        """Test EPUB file ingestion."""
        test_file = self.test_data_dir / "sample_epub.epub"
        if not test_file.exists():
            pytest.skip(f"Test file {test_file} not found")
        
        document_id = self._ingest_file(
            file_path=test_file,
            content_type='application/epub+zip',
            build_kg=True
        )
        
        # Verify processing
        self._wait_for_processing(document_id)
        self._verify_document_processing(document_id, expect_kg=True)
        
        self.processed_documents.append(document_id)
    
    def test_url_ingestion(self):
        """Test URL ingestion."""
        url_file = self.test_data_dir / "sample_url.txt"
        if not url_file.exists():
            pytest.skip(f"URL file {url_file} not found")
        
        with open(url_file, 'r') as f:
            url = f.read().strip()
        
        # Ingest URL
        data = {
            'url': url,
            'build_knowledge_graph': True,
            'chunking_strategy': 'standard'
        }
        
        response = requests.post(
            f"{self.api_base_url}/ingest/url",
            json=data,
            timeout=30
        )
        
        assert response.status_code == 200
        result = response.json()
        document_id = result["document_id"]
        
        # Verify processing
        self._wait_for_processing(document_id)
        self._verify_document_processing(document_id, expect_kg=True)
        
        self.processed_documents.append(document_id)
    
    def test_pubmed_ingestion_batch(self):
        """Test PubMed ingestion with batch processing."""
        # Ingest PubMed articles
        data = {
            'query': 'longevity diet',
            'max_results': 2,
            'build_knowledge_graph': True,
            'use_batch_api': True
        }
        
        response = requests.post(
            f"{self.api_base_url}/ingest/pubmed",
            json=data,
            timeout=30
        )
        
        assert response.status_code == 200
        result = response.json()
        
        # Wait for PubMed processing
        time.sleep(15)
        
        # Get documents from database
        documents = self._get_pubmed_documents()
        assert len(documents) > 0, "No PubMed documents found"
        
        # Verify at least one document
        for doc in documents[:1]:
            self._verify_document_processing(doc['id'], expect_kg=True)
            self.processed_documents.append(doc['id'])
    
    def test_batch_processing_workflow(self):
        """Test that batch processing workflow is working."""
        # Check for vertex_ai_batch_job tasks
        with DataTransport() as transport:
            # Get batch job tasks
            db = transport.db_client.db
            result = db.execute(text("""
                SELECT id, task_type, status, result 
                FROM processing_tasks 
                WHERE task_type = 'vertex_ai_batch_job'
                ORDER BY created_at DESC
                LIMIT 5
            """))
            
            batch_tasks = result.fetchall()
            
            if batch_tasks:
                print(f"Found {len(batch_tasks)} batch job tasks")
                for task in batch_tasks:
                    print(f"Task {task[0]}: {task[1]} - {task[2]}")
            else:
                print("No batch job tasks found - this may be expected if no KG processing occurred")
    
    def test_knowledge_graph_entities(self):
        """Test that entities are created in Neo4j."""
        if not self.processed_documents:
            pytest.skip("No processed documents to check")
        
        try:
            neo4j_service = Neo4jService()
            with neo4j_service.driver.session() as session:
                # Count entities
                result = session.run("MATCH (e:Entity) RETURN count(e) as count")
                entity_count = result.single()['count']
                
                # Count chunks
                result = session.run("MATCH (c:Chunk) RETURN count(c) as count")
                chunk_count = result.single()['count']
                
                # Count documents
                result = session.run("MATCH (d:Document) RETURN count(d) as count")
                doc_count = result.single()['count']
                
                print(f"Neo4j: {entity_count} entities, {chunk_count} chunks, {doc_count} documents")
                
                # We expect at least some data if KG processing worked
                if entity_count > 0 or chunk_count > 0:
                    print("✅ Knowledge graph data found")
                else:
                    print("⚠️  No knowledge graph data found - may indicate batch processing is still running")
            
            neo4j_service.close()
            
        except Exception as e:
            print(f"Warning: Could not check Neo4j: {e}")
    
    def _ingest_file(self, file_path: Path, content_type: str, build_kg: bool = True) -> str:
        """Ingest a file and return document ID."""
        with open(file_path, 'rb') as f:
            files = {'file': (file_path.name, f, content_type)}
            data = {
                'build_knowledge_graph': str(build_kg).lower(),
                'chunking_strategy': 'standard',
                'use_batch_api': 'true'
            }
            
            response = requests.post(
                f"{self.api_base_url}/ingest/upload",
                files=files,
                data=data,
                timeout=30
            )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "document_id" in result
        return result["document_id"]
    
    def _wait_for_processing(self, document_id: str, timeout: int = 120):
        """Wait for document processing to complete."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.api_base_url}/documents/{document_id}")
                if response.status_code == 200:
                    doc_data = response.json()
                    status = doc_data.get("status", "unknown")
                    print(f"Document {document_id} status: {status}")
                    
                    if status in ["processed", "completed"]:
                        return
                    elif status in ["failed", "error"]:
                        raise Exception(f"Document processing failed with status: {status}")
            except Exception as e:
                print(f"Error checking document status: {e}")
            
            time.sleep(5)
        
        print(f"Warning: Document {document_id} processing timeout after {timeout} seconds")
    
    def _verify_document_processing(self, document_id: str, expect_kg: bool = False):
        """Verify document was processed correctly."""
        db = next(get_db())
        try:
            # Check document exists
            result = db.execute(text("SELECT * FROM documents WHERE id = :doc_id"), {"doc_id": document_id})
            document = result.fetchone()
            assert document is not None, f"Document {document_id} not found"
            
            # Check RAG chunks
            result = db.execute(text("SELECT COUNT(*) FROM rag_chunks WHERE document_id = :doc_id"), {"doc_id": document_id})
            rag_count = result.scalar()
            assert rag_count > 0, f"No RAG chunks found for document {document_id}"
            print(f"✅ Document {document_id}: {rag_count} RAG chunks")
            
            # Check KG chunks if expected
            if expect_kg:
                result = db.execute(text("SELECT COUNT(*) FROM kg_chunks WHERE document_id = :doc_id"), {"doc_id": document_id})
                kg_count = result.scalar()
                if kg_count > 0:
                    print(f"✅ Document {document_id}: {kg_count} KG chunks")
                else:
                    print(f"⚠️  Document {document_id}: No KG chunks (may be processing)")
            
        finally:
            db.close()
    
    def _get_pubmed_documents(self) -> List[Dict[str, Any]]:
        """Get PubMed documents from database."""
        db = next(get_db())
        try:
            result = db.execute(text("""
                SELECT id, title, metadata 
                FROM documents 
                WHERE metadata->>'source'->>'type' = 'pubmed'
                OR title LIKE '%longevity%' 
                OR title LIKE '%diet%'
            """))
            
            documents = []
            for row in result.fetchall():
                documents.append({
                    'id': str(row[0]),
                    'title': row[1],
                    'metadata': row[2] if row[2] else {}
                })
            return documents
        finally:
            db.close()


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v", "-s"])
