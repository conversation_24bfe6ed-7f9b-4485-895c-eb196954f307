import pytest

from common.task_definitions import TaskType, TaskStatus, TaskTransitionMap

class TestTaskType:
    def test_vertex_ai_batch_job_value(self):
        assert TaskType.VERTEX_AI_BATCH_JOB.value == "vertex_ai_batch_job"

class TestTaskTransitionMap:
    # Transitions for VERTEX_AI_BATCH_JOB
    # _VERTEX_AI_BATCH_JOB_TRANSITIONS = {
    #     TaskStatus.CREATED.value: [TaskStatus.PROCESSING.value, TaskStatus.FAILED.value, TaskStatus.COMPLETED.value],
    #     TaskStatus.PROCESSING.value: [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value],
    #     TaskStatus.COMPLETED.value: [],
    #     TaskStatus.FAILED.value: [TaskStatus.RETRY.value]
    # }

    @pytest.mark.parametrize(
        "current_status, new_status, expected",
        [
            (TaskStatus.CREATED.value, TaskStatus.PROCESSING.value, True),
            (TaskStatus.CREATED.value, TaskStatus.FAILED.value, True),
            (TaskStatus.CREATED.value, TaskStatus.COMPLETED.value, True), # Direct completion
            (TaskStatus.PROCESSING.value, TaskStatus.COMPLETED.value, True),
            (TaskStatus.PROCESSING.value, TaskStatus.FAILED.value, True),
            (TaskStatus.FAILED.value, TaskStatus.RETRY.value, True),
            # Invalid transitions
            (TaskStatus.COMPLETED.value, TaskStatus.PROCESSING.value, False),
            (TaskStatus.CREATED.value, TaskStatus.CREATED.value, False),
            (TaskStatus.PROCESSING.value, TaskStatus.CREATED.value, False),
            (TaskStatus.BATCH_JOB_SUBMITTED.value, TaskStatus.CREATED.value, False), # Different task type's status
        ],
    )
    def test_is_valid_transition_vertex_ai_batch_job(
        self, current_status, new_status, expected
    ):
        assert (
            TaskTransitionMap.is_valid_transition(
                current_status, new_status, task_type=TaskType.VERTEX_AI_BATCH_JOB.value
            )
            == expected
        )

    def test_get_allowed_transitions_vertex_ai_batch_job(self):
        # CREATED
        allowed_from_created = TaskTransitionMap.get_allowed_transitions(
            TaskStatus.CREATED.value, task_type=TaskType.VERTEX_AI_BATCH_JOB.value
        )
        assert set(allowed_from_created) == {
            TaskStatus.PROCESSING.value, TaskStatus.FAILED.value, TaskStatus.COMPLETED.value
        }

        # PROCESSING
        allowed_from_processing = TaskTransitionMap.get_allowed_transitions(
            TaskStatus.PROCESSING.value, task_type=TaskType.VERTEX_AI_BATCH_JOB.value
        )
        assert set(allowed_from_processing) == {
            TaskStatus.COMPLETED.value, TaskStatus.FAILED.value
        }

        # COMPLETED
        allowed_from_completed = TaskTransitionMap.get_allowed_transitions(
            TaskStatus.COMPLETED.value, task_type=TaskType.VERTEX_AI_BATCH_JOB.value
        )
        assert set(allowed_from_completed) == set()

        # FAILED
        allowed_from_failed = TaskTransitionMap.get_allowed_transitions(
            TaskStatus.FAILED.value, task_type=TaskType.VERTEX_AI_BATCH_JOB.value
        )
        assert set(allowed_from_failed) == {TaskStatus.RETRY.value}

    def test_is_valid_transition_generic_transitions(self):
        # Test a generic transition that should be allowed for any task type if not overridden
        assert TaskTransitionMap.is_valid_transition(TaskStatus.PENDING.value, TaskStatus.CREATED.value) is True
        # Test a generic transition for VERTEX_AI_BATCH_JOB that is NOT overridden by its specific map
        # (e.g. PENDING -> CREATED is part of _BASE_TRANSITIONS and not in _VERTEX_AI_BATCH_JOB_TRANSITIONS)
        assert TaskTransitionMap.is_valid_transition(
            TaskStatus.PENDING.value, TaskStatus.CREATED.value, task_type=TaskType.VERTEX_AI_BATCH_JOB.value
        ) is True
        # Test a transition that is overridden and NOT allowed for VERTEX_AI_BATCH_JOB
        # Example: UPLOADED -> CHUNKING is in _BASE_TRANSITIONS but UPLOADED is not a valid state for VERTEX_AI_BATCH_JOB
        assert TaskTransitionMap.is_valid_transition(
            TaskStatus.UPLOADED.value, TaskStatus.CHUNKING.value, task_type=TaskType.VERTEX_AI_BATCH_JOB.value
        ) is False # Because UPLOADED is not a defined starting state for VERTEX_AI_BATCH_JOB's specific transitions
                                # and _ALL_TRANSITIONS.update(_VERTEX_AI_BATCH_JOB_TRANSITIONS) means
                                # if UPLOADED was a key in _VERTEX_AI_BATCH_JOB_TRANSITIONS, it would take precedence.
                                # Since it's not, it defaults to the base, but the task_type specific filtering might also apply.
                                # The current implementation of get_allowed_transitions doesn't have specific filtering
                                # *against* VERTEX_AI_BATCH_JOB, so this will be True if UPLOADED is in _ALL_TRANSITIONS.
                                # Let's re-evaluate: _ALL_TRANSITIONS.get(TaskStatus.UPLOADED.value) will return base transitions.
                                # The specific task_type filtering in get_allowed_transitions for VERTEX_AI_BATCH_JOB is:
                                # `if task_type and task_type.startswith(TaskType.KNOWLEDGE_GRAPH.value):` which does not apply.
                                # So, it should return the base transitions.
                                # The key is that _VERTEX_AI_BATCH_JOB_TRANSITIONS completely defines the lifecycle from CREATED.
                                # Any state not in its keys (CREATED, PROCESSING, COMPLETED, FAILED) will use _BASE_TRANSITIONS.
                                # So TaskStatus.UPLOADED.value for task_type VERTEX_AI_BATCH_JOB will use _BASE_TRANSITIONS.
        # Let's test a specific overridden case:
        # For a generic task, PROCESSED -> COMPLETED is allowed by _BASE_TRANSITIONS
        assert TaskTransitionMap.is_valid_transition(TaskStatus.PROCESSED.value, TaskStatus.COMPLETED.value) is True

        # For a KNOWLEDGE_GRAPH task, PROCESSED -> COMPLETED is NOT allowed directly
        assert TaskTransitionMap.is_valid_transition(TaskStatus.PROCESSED.value, TaskStatus.COMPLETED.value, task_type=TaskType.KNOWLEDGE_GRAPH.value) is False
        assert TaskTransitionMap.is_valid_transition(TaskStatus.PROCESSED.value, TaskStatus.BATCH_JOB_SUBMITTED.value, task_type=TaskType.KNOWLEDGE_GRAPH.value) is True

        # For VERTEX_AI_BATCH_JOB, PROCESSED is not a defined state in its lifecycle, so it would fall back to _BASE_TRANSITIONS.
        # This means PROCESSED -> COMPLETED would be true. This is fine as VERTEX_AI_BATCH_JOB has its own lifecycle starting from CREATED.
        assert TaskTransitionMap.is_valid_transition(TaskStatus.PROCESSED.value, TaskStatus.COMPLETED.value, task_type=TaskType.VERTEX_AI_BATCH_JOB.value) is True

    def test_get_allowed_transitions_knowledge_graph_specific(self):
        # Test the specific filtering for KNOWLEDGE_GRAPH tasks
        allowed_from_processed_kg = TaskTransitionMap.get_allowed_transitions(
            TaskStatus.PROCESSED.value, task_type=TaskType.KNOWLEDGE_GRAPH.value
        )
        # Should not include COMPLETED directly, must go via BATCH_JOB_SUBMITTED
        assert TaskStatus.COMPLETED.value not in allowed_from_processed_kg
        assert TaskStatus.BATCH_JOB_SUBMITTED.value in allowed_from_processed_kg

        allowed_from_processed_other = TaskTransitionMap.get_allowed_transitions(
            TaskStatus.PROCESSED.value, task_type=TaskType.DOCUMENT_PROCESSING_TEXT.value # Some other task type
        )
        # Should include COMPLETED for other task types if defined in base
        assert TaskStatus.COMPLETED.value in allowed_from_processed_other

        # Test a sub-type of KNOWLEDGE_GRAPH
        allowed_from_processed_kg_sub = TaskTransitionMap.get_allowed_transitions(
            TaskStatus.PROCESSED.value, task_type=f"{TaskType.KNOWLEDGE_GRAPH.value}.some_subtype"
        )
        assert TaskStatus.COMPLETED.value not in allowed_from_processed_kg_sub
        assert TaskStatus.BATCH_JOB_SUBMITTED.value in allowed_from_processed_kg_sub

    def test_all_transitions_updated(self):
        # Check that _ALL_TRANSITIONS was updated correctly
        # For example, TaskStatus.CREATED for a VERTEX_AI_BATCH_JOB should use the specific transitions
        allowed_transitions = TaskTransitionMap._ALL_TRANSITIONS.get(TaskStatus.CREATED.value, [])

        # For VERTEX_AI_BATCH_JOB, these should be the specific ones.
        # The _ALL_TRANSITIONS.update(_VERTEX_AI_BATCH_JOB_TRANSITIONS) ensures this.
        expected_for_vertex_ai_created = {
            TaskStatus.PROCESSING.value, TaskStatus.FAILED.value, TaskStatus.COMPLETED.value
        }
        # We need to be careful here. _ALL_TRANSITIONS.get(TaskStatus.CREATED.value) will return the merged list.
        # The get_allowed_transitions method then filters this.
        # The test for get_allowed_transitions_vertex_ai_batch_job already covers the effective outcome.
        # This test is more about the internal state of _ALL_TRANSITIONS.

        # _ALL_TRANSITIONS[TaskStatus.CREATED.value] should be the list from _VERTEX_AI_BATCH_JOB_TRANSITIONS
        # because of the .update() call.
        assert set(TaskTransitionMap._ALL_TRANSITIONS[TaskStatus.CREATED.value]) == expected_for_vertex_ai_created

        # A state from _BASE_TRANSITIONS not in _VERTEX_AI_BATCH_JOB_TRANSITIONS should remain from _BASE_TRANSITIONS
        # e.g., TaskStatus.PENDING.value
        expected_for_pending = {TaskStatus.CREATED.value, TaskStatus.QUEUED.value, TaskStatus.UPLOADING.value}
        assert set(TaskTransitionMap._ALL_TRANSITIONS[TaskStatus.PENDING.value]) == expected_for_pending

        # A state from _KG_TRANSITIONS not in _VERTEX_AI_BATCH_JOB_TRANSITIONS should remain from _KG_TRANSITIONS
        # e.g. TaskStatus.BATCH_JOB_COMPLETED
        expected_for_kg_batch_job_completed = {TaskStatus.KG_PROCESSING_STARTED.value, TaskStatus.FAILED.value}
        assert set(TaskTransitionMap._ALL_TRANSITIONS[TaskStatus.BATCH_JOB_COMPLETED.value]) == expected_for_kg_batch_job_completed

        # A state from _KG_TRANSITIONS that IS in _VERTEX_AI_BATCH_JOB_TRANSITIONS (e.g. TaskStatus.COMPLETED)
        # should be overridden by _VERTEX_AI_BATCH_JOB_TRANSITIONS.
        # _KG_TRANSITIONS[TaskStatus.COMPLETED.value] is not defined, so it's not overridden in this case.
        # _VERTEX_AI_BATCH_JOB_TRANSITIONS[TaskStatus.COMPLETED.value] is []
        assert TaskTransitionMap._ALL_TRANSITIONS[TaskStatus.COMPLETED.value] == []

        # If TaskStatus.PROCESSING.value was in _BASE_TRANSITIONS, it would be overridden by _VERTEX_AI_BATCH_JOB_TRANSITIONS
        # _BASE_TRANSITIONS[TaskStatus.PROCESSING.value] = [TaskStatus.PROCESSED.value, TaskStatus.PROCESSING_FAILED.value, TaskStatus.FAILED.value]
        # _VERTEX_AI_BATCH_JOB_TRANSITIONS[TaskStatus.PROCESSING.value] = [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value]
        # So _ALL_TRANSITIONS[TaskStatus.PROCESSING.value] should be the latter.
        assert set(TaskTransitionMap._ALL_TRANSITIONS[TaskStatus.PROCESSING.value]) == {TaskStatus.COMPLETED.value, TaskStatus.FAILED.value}

```
