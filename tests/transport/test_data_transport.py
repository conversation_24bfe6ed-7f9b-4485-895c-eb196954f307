import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import <PERSON><PERSON><PERSON>, patch, call, ANY

from transport.data_transport import DataTransport
from common.database import ProcessingTask # Assuming this is your ORM model
from common.config import settings # For initializing DataTransport if it uses settings directly

# Fixture to mock the database session used by DataTransport
@pytest.fixture
def mock_db_session():
    session = MagicMock()
    # Mock the query chain
    query_mock = MagicMock()
    filter_mock = MagicMock()

    session.query.return_value = query_mock
    query_mock.filter.return_value = filter_mock
    # .all() will be set in individual tests
    return session

@pytest.fixture
def data_transport_instance(mock_db_session):
    # DataTransport uses a context manager which might create its own session.
    # We need to ensure our mock_db_session is used.
    # One way is to patch the __enter__ method or the session creation part.

    # If DataTransport constructor takes db_session, it's simpler:
    # dt = DataTransport(db_session=mock_db_session)
    # dt.db_client = MagicMock() # Mock the db_client part
    # dt.db_client.db = mock_db_session # Assign the session here
    # return dt

    # Based on the provided DataTransport, it creates a session in __enter__ if none is given.
    # Let's patch where it initializes DatabaseClient to ensure our mock_db_session is used.
    with patch('transport.data_transport.DatabaseClient') as MockDatabaseClient:
        mock_db_client_instance = MockDatabaseClient.return_value
        mock_db_client_instance.db = mock_db_session

        # Need to mock __enter__ to return an object that has db_client configured
        mock_transport_context = MagicMock()
        mock_transport_context.db_client = mock_db_client_instance

        with patch.object(DataTransport, '__enter__', return_value=mock_transport_context):
            # The DataTransport() call itself might not matter as much as the context managed one
            dt = DataTransport()
            yield dt # This dt isn't the one used in "with DataTransport() as transport:"
                        # The one used in "with" block is mock_transport_context
                        # So tests should use the context manager pattern


class TestDataTransportGetStaleTasks:

    def test_get_stale_tasks_basic_functionality(self, mock_db_session, data_transport_instance):
        # Arrange
        task_id_1 = uuid.uuid4()
        task_id_2 = uuid.uuid4()
        now = datetime.utcnow()

        mock_task1_updated_at = now - timedelta(hours=2)
        mock_task2_updated_at = now - timedelta(hours=3)

        mock_task1 = ProcessingTask(
            id=task_id_1,
            task_type="type1",
            status="pending",
            result={"data": "task1_data"},
            updated_at=mock_task1_updated_at,
            created_at=mock_task1_updated_at # Add created_at for to_dict()
        )
        mock_task2 = ProcessingTask(
            id=task_id_2,
            task_type="type2",
            status="processing",
            result={"info": "task2_info"},
            updated_at=mock_task2_updated_at,
            created_at=mock_task2_updated_at # Add created_at for to_dict()
        )

        # Get the filter_mock from the session mock
        filter_mock = mock_db_session.query.return_value.filter.return_value
        filter_mock.all.return_value = [mock_task1, mock_task2]

        task_types = ["type1", "type2"]
        statuses = ["pending", "processing"]
        hours_threshold = 1

        # Act
        # Use the context manager as the actual code does
        with data_transport_instance as transport: # transport here is mock_transport_context from fixture
            stale_tasks = transport.get_stale_tasks(task_types, statuses, hours_threshold)

        # Assert
        assert len(stale_tasks) == 2

        expected_task1_dict = {
            "id": str(task_id_1),
            "task_type": "type1",
            "status": "pending",
            "result": {"data": "task1_data"},
            "updated_at": mock_task1_updated_at.isoformat()
        }
        expected_task2_dict = {
            "id": str(task_id_2),
            "task_type": "type2",
            "status": "processing",
            "result": {"info": "task2_info"},
            "updated_at": mock_task2_updated_at.isoformat()
        }

        assert stale_tasks[0] == expected_task1_dict
        assert stale_tasks[1] == expected_task2_dict

        # Check that filter was called (at least once for the time being)
        # mock_db_session.query.return_value.filter.assert_called() # This checks the first filter
        assert mock_db_session.query.return_value.filter.call_count >= 1 # Should be 3 calls to filter

        # More detailed check of filter calls (optional, can be complex)
        # Example: Check the first filter call for task_type
        # first_filter_call = mock_db_session.query.return_value.filter.call_args_list[0]
        # filter_expression_tt = first_filter_call[0][0]
        # assert str(filter_expression_tt) == str(ProcessingTask.task_type.in_(task_types))


    def test_get_stale_tasks_time_filter_applied(self, mock_db_session, data_transport_instance):
        # Arrange
        filter_mock = mock_db_session.query.return_value.filter.return_value
        filter_mock.all.return_value = [] # No tasks returned, just checking filter

        hours_threshold = 2
        expected_threshold_time = datetime.utcnow() - timedelta(hours=hours_threshold)

        # Act
        with data_transport_instance as transport:
            transport.get_stale_tasks(
                task_types=["any_type"],
                statuses=["any_status"],
                hours_threshold=hours_threshold
            )

        # Assert
        # The filter method is called multiple times (task_type, status, updated_at)
        # We need to find the call that applies the time filter.
        found_time_filter = False
        # The filter calls are chained, so we inspect the calls on the mock returned by the previous filter
        # mock_db_session.query().filter(type).filter(status).filter(time)

        # The mock_db_session.query.return_value is query_mock
        # query_mock.filter.return_value is filter_mock (first call to filter)
        # filter_mock.filter.return_value is filter_mock (second call to filter)
        # filter_mock.filter.return_value is filter_mock (third call to filter for time)

        # We need to ensure that one of the filter calls was for the time threshold.
        # The structure of the mock is session.query().filter().filter().filter().all()
        # So, we check call_args_list of the object that filter is called on.

        # The filter object is what filter is called on *after* the first call.
        # So, we check the calls to filter_mock.filter

        # Let's get the actual filter calls from the mock
        # The mock_db_session.query returns query_mock.
        # query_mock.filter is the first filter.
        # query_mock.filter.return_value.filter is the second.
        # query_mock.filter.return_value.filter.return_value.filter is the third.

        # The filter calls are applied sequentially.
        # The first filter is on query_mock.
        # The second filter is on filter_mock (which is query_mock.filter.return_value).
        # The third filter is on filter_mock (which is query_mock.filter.return_value.filter.return_value).

        all_filter_calls = []
        # First call is on the object returned by query()
        all_filter_calls.extend(mock_db_session.query.return_value.filter.call_args_list)
        # Subsequent calls are on the object returned by filter() itself.
        # This is a bit tricky because the mock for filter() is the same object.
        # A better way is to have distinct mocks for each chained filter call if needed,
        # but that makes the fixture setup more complex.

        # Let's inspect the args of all calls made to the filter_mock object
        # (which represents the state *after* the first filter has been applied).
        # This means we are looking at the 2nd and 3rd filter conditions.

        # The initial query_mock.filter() handles the first condition (task_types)
        # The filter_mock.filter() handles the second (statuses) and third (time) conditions.
        # So we check filter_mock.call_args_list

        actual_filter_calls_on_filter_mock = mock_db_session.query.return_value.filter.return_value.call_args_list

        for call_args in actual_filter_calls_on_filter_mock:
            if call_args[0]: # Ensure there's an argument
                filter_arg = call_args[0][0] # The SQLAlchemy expression
                # Check for BinaryExpression for comparison filters
                if hasattr(filter_arg, 'left') and hasattr(filter_arg, 'right') and hasattr(filter_arg, 'operator'):
                    # Check if it's the 'updated_at' filter
                    # The exact string representation can be fragile.
                    # A more robust way is to check the 'key' of the left ColumnElement
                    if hasattr(filter_arg.left, 'key') and filter_arg.left.key == 'updated_at':
                        assert filter_arg.operator.__name__ == 'lt' # SQLAlchemy operator for <
                        # Allow a small delta for the time comparison (e.g., 5 seconds)
                        assert abs(filter_arg.right.value - expected_threshold_time).total_seconds() < 5
                        found_time_filter = True
                        break
        assert found_time_filter, "Time filter on ProcessingTask.updated_at was not applied as expected."


    def test_get_stale_tasks_empty_result(self, mock_db_session, data_transport_instance):
        # Arrange
        filter_mock = mock_db_session.query.return_value.filter.return_value
        filter_mock.all.return_value = [] # Simulate no tasks found

        # Act
        with data_transport_instance as transport:
            stale_tasks = transport.get_stale_tasks(
                task_types=["type1"],
                statuses=["status1"],
                hours_threshold=1
            )

        # Assert
        assert len(stale_tasks) == 0
        filter_mock.all.assert_called_once()


    @patch('transport.data_transport.logger.error')
    def test_get_stale_tasks_exception_handling(self, mock_logger_error, mock_db_session, data_transport_instance):
        # Arrange
        # Make the query attempt raise an exception
        mock_db_session.query.side_effect = Exception("Database query failed")

        # Act
        with data_transport_instance as transport:
            stale_tasks = transport.get_stale_tasks(
                task_types=["type1"],
                statuses=["status1"],
                hours_threshold=1
            )

        # Assert
        assert len(stale_tasks) == 0
        mock_logger_error.assert_called_once()
        # Check if the log message contains the expected error string part
        assert "Error fetching stale tasks: Database query failed" in mock_logger_error.call_args[0][0]

```
