import pytest
from unittest.mock import patch, MagicMock, call, ANY
import uuid
import time

from common.task_definitions import TaskStatus, TaskType
from services.knowledge_graph_service import KnowledgeGraphService
from common.database import ProcessingTask, Document, Chunk # Assuming these are needed for mocks
from google.cloud.aiplatform.compat.types import job_state as gca_job_state


@pytest.fixture
def mock_data_transport():
    with patch('services.knowledge_graph_service.DataTransport') as MockDataTransport:
        mock_transport_instance = MockDataTransport.return_value.__enter__.return_value
        mock_transport_instance.db_client = MagicMock()
        mock_transport_instance.neo4j_client = MagicMock()
        yield mock_transport_instance

@pytest.fixture
def mock_vertex_ai_batch_client():
    with patch('services.knowledge_graph_service.VertexAIBatchClient') as MockVertexAIBatchClient:
        mock_vertex_client_instance = MockVertexAIBatchClient.return_value
        yield mock_vertex_client_instance

@pytest.fixture
def mock_nlp_service():
    with patch('services.knowledge_graph_service.NLPService') as MockNLPService:
        yield MockNLPService

@pytest.fixture
def mock_entity_processor():
    with patch('services.knowledge_graph_service.EntityRelationshipProcessor') as MockProcessor:
        yield MockProcessor.return_value


@pytest.fixture
def kg_service(mock_entity_processor): # Processor is part of the constructor
    service = KnowledgeGraphService(processor=mock_entity_processor)
    return service

class TestKnowledgeGraphService:

    def test_build_async_success(
        self, kg_service, mock_data_transport, mock_vertex_ai_batch_client
    ):
        # Arrange
        doc_id = uuid.uuid4()
        task_id = uuid.uuid4()

        mock_document = Document(id=doc_id, name="Test Doc", type="text/plain", status="uploaded")
        mock_data_transport.db_client.get_document.return_value = mock_document

        mock_chunks = [{"id": uuid.uuid4(), "text": "chunk text"}]
        mock_data_transport.get_document_chunks.return_value = mock_chunks

        mock_vertex_ai_batch_client.batch_process_chunks.return_value = {
            "status": "submitted",
            "batch_job_id": "vertex_job_123"
        }

        # Act
        result = kg_service._build_async(document_id=doc_id, task_id=task_id)

        # Assert
        # Initial status update to PROCESSING
        mock_data_transport.update_task_status.assert_any_call(task_id=task_id, status=TaskStatus.PROCESSING.value)

        # Final status update to BATCH_JOB_SUBMITTED
        mock_data_transport.update_task_status.assert_called_with(
            task_id=task_id,
            status=TaskStatus.BATCH_JOB_SUBMITTED.value,
            result={
                "batch_job_id": "vertex_job_123",
                "status": TaskStatus.BATCH_JOB_SUBMITTED.value,
                "created_at": pytest.approx(time.time(), abs=1),
                "async_processing": True
            }
        )
        assert result["status"] == TaskStatus.BATCH_JOB_SUBMITTED.value
        assert result["batch_job_id"] == "vertex_job_123"

    def test_check_and_continue_batch_jobs_job_succeeds(
        self, kg_service, mock_data_transport, mock_vertex_ai_batch_client
    ):
        # Arrange
        kg_task_id = uuid.uuid4()
        batch_job_id = "vertex_job_abc"
        doc_id = uuid.uuid4()

        mock_kg_task = ProcessingTask(
            id=kg_task_id,
            document_id=doc_id,
            task_type=TaskType.KNOWLEDGE_GRAPH_BUILDING.value,
            status=TaskStatus.BATCH_JOB_SUBMITTED.value, # Starts in submitted
            result={"batch_job_id": batch_job_id, "async_processing": True}
        )
        # Simulate the query for KG tasks
        mock_data_transport.db_client.db.query.return_value.filter.return_value.limit.return_value.all.return_value = [mock_kg_task]

        mock_vertex_ai_task_id = uuid.uuid4()
        mock_processed_results = [{"chunk_id": "chunk1", "entities": [{"text": "Entity1"}]}]
        mock_vertex_ai_task = ProcessingTask(
            id=mock_vertex_ai_task_id,
            task_type=TaskType.VERTEX_AI_BATCH_JOB.value,
            status=TaskStatus.COMPLETED.value,
            result={"batch_job_id": batch_job_id, "processed_entity_results": mock_processed_results}
        )
        # Simulate the query for the vertex_ai_batch_job task (this is now part of _fetch_vertex_task, which _process_single_kg_task calls)
        # mock_data_transport.db_client.db.query.return_value.filter.return_value.first.return_value = mock_vertex_ai_task

        # We mock the helper methods called by check_and_continue_batch_jobs
        with patch.object(kg_service, '_get_actionable_kg_tasks', return_value=[mock_kg_task]) as mock_get_tasks, \
             patch.object(kg_service, '_process_single_kg_task', return_value=True) as mock_process_task: # Simulate it continued to build

            # Act
            response = kg_service.check_and_continue_batch_jobs()

            # Assert
            mock_get_tasks.assert_called_once()
            mock_process_task.assert_called_once_with(mock_kg_task, mock_data_transport)

            assert response["processed_tasks_count"] == 1
            assert response["continued_to_kg_build_count"] == 1 # Because _process_single_kg_task returned True

    def test_check_and_continue_batch_jobs_multiple_tasks(
        self, kg_service, mock_data_transport
    ):
        # Arrange
        mock_task1 = ProcessingTask(id=uuid.uuid4(), task_type=TaskType.KNOWLEDGE_GRAPH_BUILDING.value, status=TaskStatus.BATCH_JOB_SUBMITTED.value, result={"batch_job_id": "job1", "async_processing": True})
        mock_task2 = ProcessingTask(id=uuid.uuid4(), task_type=TaskType.KNOWLEDGE_GRAPH_BUILDING.value, status=TaskStatus.BATCH_JOB_PROCESSING.value, result={"batch_job_id": "job2", "async_processing": True})

        with patch.object(kg_service, '_get_actionable_kg_tasks', return_value=[mock_task1, mock_task2]) as mock_get_tasks, \
             patch.object(kg_service, '_process_single_kg_task') as mock_process_task:

            # Simulate first task continues, second does not (e.g. still processing)
            mock_process_task.side_effect = [True, False]

            # Act
            response = kg_service.check_and_continue_batch_jobs()

            # Assert
            mock_get_tasks.assert_called_once()
            assert mock_process_task.call_count == 2
            mock_process_task.assert_any_call(mock_task1, mock_data_transport)
            mock_process_task.assert_any_call(mock_task2, mock_data_transport)

            assert response["processed_tasks_count"] == 2
            assert response["continued_to_kg_build_count"] == 1

    def test_check_and_continue_batch_jobs_no_actionable_tasks(
        self, kg_service, mock_data_transport
    ):
        # Arrange
        with patch.object(kg_service, '_get_actionable_kg_tasks', return_value=[]) as mock_get_tasks, \
             patch.object(kg_service, '_process_single_kg_task') as mock_process_task:

            # Act
            response = kg_service.check_and_continue_batch_jobs()

            # Assert
            mock_get_tasks.assert_called_once()
            mock_process_task.assert_not_called()

            assert response["processed_tasks_count"] == 0
            assert response["continued_to_kg_build_count"] == 0
            assert response["message"] == "No actionable tasks."

    def test_continue_knowledge_graph_building_with_prefetched_results(
        self, kg_service, mock_data_transport, mock_vertex_ai_batch_client, mock_nlp_service, mock_entity_processor
    ):
        # Arrange
        task_id = uuid.uuid4()
        doc_id = uuid.uuid4()
        mock_prefetched_results = [{"chunk_id": "chunk1", "entities": [{"text": "Prefetched Entity"}]}]

        mock_task = ProcessingTask(id=task_id, document_id=doc_id, task_type=TaskType.KNOWLEDGE_GRAPH_BUILDING.value, result={"batch_job_id": "job123", "status": TaskStatus.BATCH_JOB_COMPLETED.value})
        mock_data_transport.db_client.get_processing_task.return_value = mock_task

        mock_document = Document(id=doc_id)
        mock_data_transport.db_client.get_document.return_value = mock_document

        mock_chunks = [{"id": "chunk1", "text": "text"}]
        mock_data_transport.get_document_chunks.return_value = mock_chunks

        mock_entity_processor.process_batch_results.return_value = (mock_prefetched_results, []) # (entities, relationships)
        mock_nlp_service.deduplicate_and_normalize_entities.return_value = mock_prefetched_results


        # Act
        kg_service.continue_knowledge_graph_building(task_id=task_id, processed_entity_results=mock_prefetched_results)

        # Assert
        # 1. Initial status update to KG_PROCESSING_STARTED
        #    The call list will include the BATCH_JOB_COMPLETED from check_and_continue, then KG_PROCESSING_STARTED
        update_calls = mock_data_transport.update_task_status.call_args_list

        expected_kg_processing_started_call = call(
            task_id=task_id,
            status=TaskStatus.KG_PROCESSING_STARTED.value,
            result={
                "batch_job_id": "job123", # from mock_task.result
                "status": TaskStatus.KG_PROCESSING_STARTED.value,
                "kg_processing_started_at": pytest.approx(time.time(), abs=1)
            }
        )
        assert expected_kg_processing_started_call in update_calls

        # 2. VertexAI client's get_batch_results NOT called
        mock_vertex_ai_batch_client.get_batch_results.assert_not_called()

        # 3. Entity processor called with prefetched results
        mock_entity_processor.process_batch_results.assert_called_once_with(mock_prefetched_results, doc_id)

        # 4. Final status update to COMPLETED
        expected_completed_call = call(
            task_id=task_id,
            status=TaskStatus.COMPLETED.value,
            result=ANY # Result dict is complex, just check status and task_id
        )
        # Check the last call specifically for the final status
        final_call_args = update_calls[-1]
        assert final_call_args[1]['task_id'] == task_id
        assert final_call_args[1]['status'] == TaskStatus.COMPLETED.value
        assert final_call_args[1]['result']['status'] == TaskStatus.COMPLETED.value
        assert final_call_args[1]['result']['entities_count'] == len(mock_prefetched_results)


    def test_continue_knowledge_graph_building_fallback_to_get_batch_results(
        self, kg_service, mock_data_transport, mock_vertex_ai_batch_client, mock_nlp_service, mock_entity_processor
    ):
        # Arrange
        task_id = uuid.uuid4()
        doc_id = uuid.uuid4()
        batch_job_id = "job_fallback"

        # Task result does *not* contain 'batch_results' (string format) and no prefetched results are passed
        mock_task_result_initial = {"batch_job_id": batch_job_id, "status": TaskStatus.BATCH_JOB_COMPLETED.value}
        mock_task = ProcessingTask(id=task_id, document_id=doc_id, task_type=TaskType.KNOWLEDGE_GRAPH_BUILDING.value, result=mock_task_result_initial)
        mock_data_transport.db_client.get_processing_task.return_value = mock_task

        mock_document = Document(id=doc_id)
        mock_data_transport.db_client.get_document.return_value = mock_document

        mock_chunks = [{"id": "chunk1", "text": "text"}]
        mock_data_transport.get_document_chunks.return_value = mock_chunks

        # Mock the GCS/Vertex AI call
        mock_fetched_results_from_vertex = [{"chunk_id": "chunk1", "entities": [{"text": "Fetched From Vertex"}]}]
        mock_vertex_ai_batch_client.get_batch_results.return_value = mock_fetched_results_from_vertex

        mock_entity_processor.process_batch_results.return_value = (mock_fetched_results_from_vertex, [])
        mock_nlp_service.deduplicate_and_normalize_entities.return_value = mock_fetched_results_from_vertex

        # Act
        kg_service.continue_knowledge_graph_building(task_id=task_id, processed_entity_results=None) # Explicitly pass None

        # Assert
        # 1. VertexAI client's get_batch_results WAS called
        mock_vertex_ai_batch_client.get_batch_results.assert_called_once_with(batch_job_id, mock_chunks)

        # 2. Entity processor called with results from Vertex
        mock_entity_processor.process_batch_results.assert_called_once_with(mock_fetched_results_from_vertex, doc_id)

        # 3. Final status update to COMPLETED (check relevant parts)
        update_calls = mock_data_transport.update_task_status.call_args_list
        # The first call is to KG_PROCESSING_STARTED, the second is to COMPLETED
        assert len(update_calls) >= 2 # Ensure at least two updates occurred

        # Check KG_PROCESSING_STARTED call
        kg_processing_started_call = update_calls[-2] # Second to last call
        assert kg_processing_started_call[1]['task_id'] == task_id
        assert kg_processing_started_call[1]['status'] == TaskStatus.KG_PROCESSING_STARTED.value
        assert kg_processing_started_call[1]['result']['status'] == TaskStatus.KG_PROCESSING_STARTED.value
        assert kg_processing_started_call[1]['result']['batch_job_id'] == batch_job_id # Original result preserved

        # Check COMPLETED call
        final_call_args = update_calls[-1] # Last call
        assert final_call_args[1]['task_id'] == task_id
        assert final_call_args[1]['status'] == TaskStatus.COMPLETED.value
        assert final_call_args[1]['result']['status'] == TaskStatus.COMPLETED.value
        assert final_call_args[1]['result']['entities_count'] == len(mock_fetched_results_from_vertex)

```
