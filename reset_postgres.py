#!/usr/bin/env python3
"""
Reset PostgreSQL database using environment variables from .env file.
"""
import os
import sys
import psycopg2
from pathlib import Path

def load_env_vars():
    """Load environment variables from .env file."""
    env_path = Path('.env')
    if not env_path.exists():
        print("❌ .env file not found")
        return False
    
    try:
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    value = value.strip('"\'')
                    os.environ[key] = value
        print("✅ Environment variables loaded from .env file")
        return True
    except Exception as e:
        print(f"❌ Error loading .env file: {e}")
        return False

def reset_postgres():
    """Reset PostgreSQL database."""
    # Load environment variables
    if not load_env_vars():
        return False
    
    # Get connection parameters
    postgres_host = "localhost"  # When running outside Docker
    postgres_port = os.getenv("POSTGRES_PORT", "5432")
    postgres_db = os.getenv("POSTGRES_DB", "longevity")
    postgres_user = os.getenv("POSTGRES_USER", "longevity")
    postgres_password = os.getenv("POSTGRES_PASSWORD")
    
    if not postgres_password:
        print("❌ POSTGRES_PASSWORD not found in environment variables")
        return False
    
    print(f"🔗 Connecting to PostgreSQL at {postgres_host}:{postgres_port}")
    print(f"   Database: {postgres_db}")
    print(f"   User: {postgres_user}")
    
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=postgres_host,
            port=postgres_port,
            database=postgres_db,
            user=postgres_user,
            password=postgres_password
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("✅ Connected to PostgreSQL successfully")
        
        # List of tables to clean in correct order (respecting foreign keys)
        tables_to_clean = [
            "relationships",
            "entities", 
            "kg_chunks",
            "chunks",
            "processing_tasks",
            "documents"
        ]
        
        print("🧹 Cleaning database tables...")
        
        for table in tables_to_clean:
            try:
                cursor.execute(f"DELETE FROM {table}")
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ Cleaned table '{table}' (remaining rows: {count})")
            except Exception as e:
                print(f"   ⚠️  Could not clean table '{table}': {e}")
        
        cursor.close()
        conn.close()
        
        print("✅ PostgreSQL database reset completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error resetting PostgreSQL database: {e}")
        return False

def reset_neo4j():
    """Reset Neo4j database."""
    try:
        from neo4j import GraphDatabase
        
        # Get Neo4j connection parameters
        neo4j_host = "localhost"  # When running outside Docker
        neo4j_port = os.getenv("NEO4J_PORT", "7687")
        neo4j_user = os.getenv("NEO4J_USER", "neo4j")
        neo4j_auth = os.getenv("NEO4J_AUTH", "")
        
        if not neo4j_auth:
            print("❌ NEO4J_AUTH not found in environment variables")
            return False
        
        # Parse NEO4J_AUTH (format: user/password)
        if '/' in neo4j_auth:
            neo4j_user, neo4j_password = neo4j_auth.split('/', 1)
        else:
            print("❌ Invalid NEO4J_AUTH format (expected: user/password)")
            return False
        
        neo4j_uri = f"neo4j://{neo4j_host}:{neo4j_port}"
        print(f"🔗 Connecting to Neo4j at {neo4j_uri}")
        print(f"   User: {neo4j_user}")
        
        # Connect to Neo4j
        driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        
        with driver.session() as session:
            # Delete all nodes and relationships
            result = session.run("MATCH (n) DETACH DELETE n")
            print("✅ Neo4j database reset completed successfully")
        
        driver.close()
        return True
        
    except ImportError:
        print("⚠️  neo4j driver not available - skipping Neo4j cleanup")
        return True
    except Exception as e:
        print(f"❌ Error resetting Neo4j database: {e}")
        return False

def main():
    """Main function."""
    print("🚀 Starting database reset...")
    print("=" * 50)
    
    postgres_success = reset_postgres()
    neo4j_success = reset_neo4j()
    
    print("=" * 50)
    if postgres_success and neo4j_success:
        print("🎉 All databases reset successfully!")
        return 0
    else:
        print("❌ Some database resets failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
