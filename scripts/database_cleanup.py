#!/usr/bin/env python3
"""
Database cleanup utility for system testing.
Cleans all data from PostgreSQL and Neo4j databases.
"""

import os
import sys
import logging
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.config import settings
from common.database import get_db, init_db
from sqlalchemy import text

# Try to import Neo4j service, handle if not available
try:
    from services.neo4j_service import Neo4jService
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    print("Warning: Neo4j service not available, will skip Neo4j cleanup")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def cleanup_postgresql() -> Dict[str, Any]:
    """
    Clean all data from PostgreSQL database.
    
    Returns:
        Dict with cleanup results
    """
    logger.info("Starting PostgreSQL database cleanup...")
    
    try:
        # Get database session
        db = next(get_db())
        
        # List of tables to clean (in order to handle foreign key constraints)
        tables_to_clean = [
            'processing_tasks',
            'kg_chunks', 
            'rag_chunks',
            'documents',
            'gdrive_processed_files',
            'entities',
            'entity_relationships'
        ]
        
        cleaned_tables = []
        total_deleted = 0
        
        for table in tables_to_clean:
            try:
                # Check if table exists
                result = db.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = '{table}'
                    );
                """))
                
                table_exists = result.scalar()
                
                if table_exists:
                    # Get count before deletion
                    count_result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count_before = count_result.scalar()
                    
                    # Delete all records
                    db.execute(text(f"DELETE FROM {table}"))
                    
                    # Get count after deletion
                    count_result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count_after = count_result.scalar()
                    
                    deleted_count = count_before - count_after
                    total_deleted += deleted_count
                    
                    cleaned_tables.append({
                        'table': table,
                        'deleted_count': deleted_count
                    })
                    
                    logger.info(f"Cleaned table '{table}': {deleted_count} records deleted")
                else:
                    logger.warning(f"Table '{table}' does not exist, skipping")
                    
            except Exception as e:
                logger.error(f"Error cleaning table '{table}': {e}")
                
        # Commit the transaction
        db.commit()
        
        logger.info(f"PostgreSQL cleanup completed. Total records deleted: {total_deleted}")
        
        return {
            'status': 'success',
            'database': 'postgresql',
            'cleaned_tables': cleaned_tables,
            'total_deleted': total_deleted
        }
        
    except Exception as e:
        logger.error(f"Error during PostgreSQL cleanup: {e}")
        return {
            'status': 'error',
            'database': 'postgresql',
            'error': str(e)
        }
    finally:
        if 'db' in locals():
            db.close()


def cleanup_neo4j() -> Dict[str, Any]:
    """
    Clean all data from Neo4j database.

    Returns:
        Dict with cleanup results
    """
    logger.info("Starting Neo4j database cleanup...")

    if not NEO4J_AVAILABLE:
        logger.warning("Neo4j service not available, skipping cleanup")
        return {
            'status': 'skipped',
            'database': 'neo4j',
            'reason': 'Neo4j service not available'
        }

    try:
        # Initialize Neo4j service
        neo4j_service = Neo4jService()

        # Get counts before cleanup
        with neo4j_service.driver.session() as session:
            # Count nodes
            node_count_result = session.run("MATCH (n) RETURN count(n) as count")
            nodes_before = node_count_result.single()['count']

            # Count relationships
            rel_count_result = session.run("MATCH ()-[r]->() RETURN count(r) as count")
            relationships_before = rel_count_result.single()['count']

            logger.info(f"Neo4j before cleanup: {nodes_before} nodes, {relationships_before} relationships")

            # Delete all relationships first
            session.run("MATCH ()-[r]->() DELETE r")

            # Delete all nodes
            session.run("MATCH (n) DELETE n")

            # Verify cleanup
            node_count_result = session.run("MATCH (n) RETURN count(n) as count")
            nodes_after = node_count_result.single()['count']

            rel_count_result = session.run("MATCH ()-[r]->() RETURN count(r) as count")
            relationships_after = rel_count_result.single()['count']

            logger.info(f"Neo4j after cleanup: {nodes_after} nodes, {relationships_after} relationships")

        neo4j_service.close()

        return {
            'status': 'success',
            'database': 'neo4j',
            'nodes_deleted': nodes_before - nodes_after,
            'relationships_deleted': relationships_before - relationships_after,
            'nodes_before': nodes_before,
            'relationships_before': relationships_before,
            'nodes_after': nodes_after,
            'relationships_after': relationships_after
        }

    except Exception as e:
        logger.error(f"Error during Neo4j cleanup: {e}")
        return {
            'status': 'error',
            'database': 'neo4j',
            'error': str(e)
        }


def cleanup_all_databases() -> Dict[str, Any]:
    """
    Clean all databases.
    
    Returns:
        Dict with cleanup results for all databases
    """
    logger.info("Starting complete database cleanup...")
    
    results = {
        'postgresql': cleanup_postgresql(),
        'neo4j': cleanup_neo4j()
    }
    
    # Summary
    total_success = sum(1 for result in results.values() if result['status'] == 'success')
    total_databases = len(results)
    
    logger.info(f"Database cleanup completed: {total_success}/{total_databases} databases cleaned successfully")
    
    return {
        'status': 'success' if total_success == total_databases else 'partial',
        'results': results,
        'summary': {
            'total_databases': total_databases,
            'successful_cleanups': total_success,
            'failed_cleanups': total_databases - total_success
        }
    }


def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Database cleanup utility for system testing')
    parser.add_argument('--database', choices=['postgresql', 'neo4j', 'all'], default='all',
                       help='Database to clean (default: all)')
    parser.add_argument('--confirm', action='store_true',
                       help='Skip confirmation prompt')
    
    args = parser.parse_args()
    
    if not args.confirm:
        response = input(f"Are you sure you want to clean {args.database} database(s)? This will delete ALL data! (yes/no): ")
        if response.lower() != 'yes':
            logger.info("Cleanup cancelled by user")
            return
    
    if args.database == 'postgresql':
        result = cleanup_postgresql()
    elif args.database == 'neo4j':
        result = cleanup_neo4j()
    else:
        result = cleanup_all_databases()
    
    print(f"\nCleanup Result: {result}")
    
    if result['status'] == 'success':
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
