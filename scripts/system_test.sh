#!/bin/bash

# System Test Script for LongevityCo Platform
# This script runs comprehensive system tests for document ingestion pipeline

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="docker-compose.vm.yml"
TEST_TIMEOUT=600  # 10 minutes

echo -e "${BLUE}=== LongevityCo System Test Pipeline ===${NC}"
echo "Project root: $PROJECT_ROOT"
echo "Compose file: $COMPOSE_FILE"
echo ""

# Function to print status
print_status() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if services are healthy
check_services() {
    print_status "Checking service health..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        print_status "Health check attempt $attempt/$max_attempts"
        
        # Check if all services are running
        local running_services=$(docker-compose -f "$COMPOSE_FILE" ps --services --filter "status=running" | wc -l)
        local total_services=$(docker-compose -f "$COMPOSE_FILE" config --services | wc -l)
        
        if [ "$running_services" -eq "$total_services" ]; then
            # Check API health endpoint
            if curl -s -f "http://localhost:8000/health" > /dev/null 2>&1; then
                print_success "All services are healthy"
                return 0
            fi
        fi
        
        print_warning "Services not ready yet, waiting..."
        sleep 10
        ((attempt++))
    done
    
    print_error "Services failed to become healthy within timeout"
    return 1
}

# Function to cleanup databases
cleanup_databases() {
    print_status "Cleaning databases..."
    
    cd "$PROJECT_ROOT"
    
    # Run database cleanup script
    if python3 scripts/database_cleanup.py --confirm; then
        print_success "Databases cleaned successfully"
    else
        print_warning "Database cleanup had some issues, continuing..."
    fi
}

# Function to run system tests
run_system_tests() {
    print_status "Running system integration tests..."
    
    cd "$PROJECT_ROOT"
    
    # Set test environment
    export PYTHONPATH="$PROJECT_ROOT"
    export TESTING=true
    
    # Run pytest with verbose output
    if python3 -m pytest tests/system/test_system_integration.py -v -s --tb=short; then
        print_success "System tests passed"
        return 0
    else
        print_error "System tests failed"
        return 1
    fi
}

# Function to validate data in databases
validate_databases() {
    print_status "Validating data in databases..."
    
    cd "$PROJECT_ROOT"
    
    # Check PostgreSQL data
    print_status "Checking PostgreSQL data..."
    python3 -c "
import sys
sys.path.insert(0, '.')
from common.database import get_db
from sqlalchemy import text

db = next(get_db())
try:
    # Check documents
    result = db.execute(text('SELECT COUNT(*) FROM documents'))
    doc_count = result.scalar()
    print(f'Documents: {doc_count}')
    
    # Check RAG chunks
    result = db.execute(text('SELECT COUNT(*) FROM rag_chunks'))
    rag_count = result.scalar()
    print(f'RAG chunks: {rag_count}')
    
    # Check KG chunks
    result = db.execute(text('SELECT COUNT(*) FROM kg_chunks'))
    kg_count = result.scalar()
    print(f'KG chunks: {kg_count}')
    
    # Check processing tasks
    result = db.execute(text('SELECT COUNT(*) FROM processing_tasks'))
    task_count = result.scalar()
    print(f'Processing tasks: {task_count}')
    
    if doc_count > 0 and rag_count > 0:
        print('✅ PostgreSQL validation passed')
    else:
        print('❌ PostgreSQL validation failed')
        sys.exit(1)
        
finally:
    db.close()
"
    
    # Check Neo4j data
    print_status "Checking Neo4j data..."
    python3 -c "
import sys
sys.path.insert(0, '.')
from services.neo4j_service import Neo4jService

try:
    neo4j_service = Neo4jService()
    with neo4j_service.driver.session() as session:
        # Count nodes
        result = session.run('MATCH (n) RETURN count(n) as count')
        node_count = result.single()['count']
        print(f'Neo4j nodes: {node_count}')
        
        # Count relationships
        result = session.run('MATCH ()-[r]->() RETURN count(r) as count')
        rel_count = result.single()['count']
        print(f'Neo4j relationships: {rel_count}')
        
        if node_count > 0:
            print('✅ Neo4j validation passed')
        else:
            print('⚠️  Neo4j has no data (may be expected for some tests)')
            
    neo4j_service.close()
except Exception as e:
    print(f'❌ Neo4j validation failed: {e}')
    sys.exit(1)
"
}

# Function to show service logs
show_service_logs() {
    print_status "Showing recent service logs..."
    
    echo -e "\n${YELLOW}=== API Logs ===${NC}"
    docker-compose -f "$COMPOSE_FILE" logs --tail=20 api
    
    echo -e "\n${YELLOW}=== Worker Logs ===${NC}"
    docker-compose -f "$COMPOSE_FILE" logs --tail=20 worker
}

# Main execution
main() {
    cd "$PROJECT_ROOT"
    
    # Step 1: Start services
    print_status "Starting Docker Compose services..."
    if docker-compose -f "$COMPOSE_FILE" up -d --build; then
        print_success "Services started"
    else
        print_error "Failed to start services"
        exit 1
    fi
    
    # Step 2: Wait for services to be healthy
    if ! check_services; then
        print_error "Services health check failed"
        show_service_logs
        exit 1
    fi
    
    # Step 3: Clean databases
    cleanup_databases
    
    # Step 4: Run system tests
    if ! run_system_tests; then
        print_error "System tests failed"
        show_service_logs
        exit 1
    fi
    
    # Step 5: Validate databases
    validate_databases
    
    print_success "System test pipeline completed successfully!"
    
    # Optional: Show final status
    print_status "Final service status:"
    docker-compose -f "$COMPOSE_FILE" ps
}

# Handle script arguments
case "${1:-}" in
    "cleanup")
        cleanup_databases
        ;;
    "test")
        run_system_tests
        ;;
    "validate")
        validate_databases
        ;;
    "logs")
        show_service_logs
        ;;
    "")
        main
        ;;
    *)
        echo "Usage: $0 [cleanup|test|validate|logs]"
        echo "  cleanup  - Clean databases only"
        echo "  test     - Run system tests only"
        echo "  validate - Validate databases only"
        echo "  logs     - Show service logs"
        echo "  (no args) - Run full system test pipeline"
        exit 1
        ;;
esac
