"""
Local development configuration settings for the longevity platform.
This file overrides the default settings in config.py for local development.
"""
from common.config import settings

# Database Settings
settings.POSTGRES_HOST = "localhost"
settings.POSTGRES_PORT = 5435
settings.DATABASE_URL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"

# Redis Settings
settings.REDIS_HOST = "localhost"
settings.REDIS_PORT = 6379
settings.REDIS_URL = f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/0"

# Neo4j Settings
settings.NEO4J_HOST = "localhost"
settings.NEO4J_PORT = 7687

# MinIO Settings
settings.MINIO_HOST = "localhost"
settings.MINIO_PORT = 9090
settings.STORAGE_URL = f"http://{settings.MINIO_HOST}:{settings.MINIO_PORT}"

# Log the configuration
print("Local development configuration loaded:")
print(f"Database URL: {settings.DATABASE_URL}")
print(f"Redis URL: {settings.REDIS_URL}")
print(f"Neo4j: {settings.NEO4J_HOST}:{settings.NEO4J_PORT}")
print(f"Storage URL: {settings.STORAGE_URL}")
