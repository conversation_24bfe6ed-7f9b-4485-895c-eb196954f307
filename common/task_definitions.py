"""
Standardized task definitions for document processing workflows.

This module defines the standard task types, status transitions, and parameters
for document processing across the system. It establishes a common taxonomy
for all document processing workflows regardless of document source.
"""
from enum import Enum
from typing import Dict, List, Optional, Any, Set


class TaskType(str, Enum):
    """
    Standardized task types hierarchy.

    This forms a taxonomy of task types with common parent types and specialized subtypes
    for specific document sources.

    Format: BASE_TYPE.SUBTYPE
    """
    # Base task types
    DOCUMENT_PROCESSING = "document_processing"
    KNOWLEDGE_GRAPH = "knowledge_graph"
    KNOWLEDGE_GRAPH_BUILDING = "knowledge_graph_building"
    ENTITY_EXTRACTION = "entity_extraction"
    RELATIONSHIP_EXTRACTION = "relationship_extraction"
    ENTITY_NORMALIZATION = "entity_normalization"
    DATA_INGESTION = "data_ingestion"

    # Document type-specific processing
    DOCUMENT_PROCESSING_TEXT = "document_processing.text"
    DOCUMENT_PROCESSING_PDF = "document_processing.pdf"
    DOCUMENT_PROCESSING_EPUB = "document_processing.epub"
    DOCUMENT_PROCESSING_HTML = "document_processing.html"
    DOCUMENT_PROCESSING_WEB = "document_processing.web"

    # Scientific document processing
    SCIENTIFIC_DOCUMENT_PROCESSING = "document_processing.scientific"
    PUBMED_PROCESSING = "document_processing.scientific.pubmed"

    # Source-specific ingestion
    GDRIVE_INGESTION = "data_ingestion.gdrive"
    URL_INGESTION = "data_ingestion.url"
    FILE_UPLOAD_INGESTION = "data_ingestion.file_upload"
    PUBMED_INGESTION = "data_ingestion.pubmed"
    VERTEX_AI_BATCH_JOB = "vertex_ai_batch_job"


class TaskStatus(str, Enum):
    """
    Standardized task status values.
    
    These statuses form a state machine that documents can progress through
    during processing.
    """
    # Initial states
    PENDING = "pending"
    CREATED = "created"
    QUEUED = "queued"
    
    # Processing states
    UPLOADING = "uploading"
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    CHUNKING = "chunking"
    CHUNKED = "chunked"
    EMBEDDING = "embedding"
    EMBEDDED = "embedded"
    
    # Knowledge Graph specific states
    BATCH_JOB_SUBMITTED = "batch_job_submitted"
    BATCH_JOB_PROCESSING = "batch_job_processing"
    BATCH_JOB_COMPLETED = "batch_job_completed"
    KG_PROCESSING_STARTED = "kg_processing_started"
    KG_PROCESSING_COMPLETED = "kg_processing_completed"
    
    # Terminal states
    COMPLETED = "completed"
    PROCESSED = "processed"
    FAILED = "failed"
    CHUNKING_FAILED = "chunking_failed"
    PROCESSING_FAILED = "processing_failed"
    KG_FAILED = "kg_failed"
    BATCH_JOB_FAILED = "batch_job_failed"
    
    # Retry states
    RETRY = "retry"
    
    @classmethod
    def get_failure_states(cls) -> Set[str]:
        """Get all failure states."""
        return {
            cls.FAILED.value,
            cls.CHUNKING_FAILED.value, 
            cls.PROCESSING_FAILED.value,
            cls.KG_FAILED.value,
            cls.BATCH_JOB_FAILED.value
        }
    
    @classmethod
    def get_terminal_states(cls) -> Set[str]:
        """Get all terminal states (success and failure)."""
        return {cls.COMPLETED.value, cls.PROCESSED.value}.union(cls.get_failure_states())
    
    @classmethod
    def get_processing_states(cls) -> Set[str]:
        """Get all active processing states."""
        return {
            cls.PROCESSING.value,
            cls.CHUNKING.value,
            cls.EMBEDDING.value, 
            cls.BATCH_JOB_PROCESSING.value,
            cls.KG_PROCESSING_STARTED.value
        }


class StandardTaskParams:
    """
    Standardized task parameters.
    
    This class defines the standard parameters that can be used 
    across all document processing tasks.
    """
    
    @staticmethod
    def create_process_options(
        build_knowledge_graph: bool = True,
        chunking_strategy: str = "standard",
        use_batch_api: bool = True,
        priority: str = "normal"
    ) -> Dict[str, Any]:
        """
        Create standardized process options dictionary.
        
        Args:
            build_knowledge_graph: Whether to build the knowledge graph
            chunking_strategy: Chunking strategy to use (standard, single_chunk, custom)
            use_batch_api: Whether to use batch API for processing
            priority: Processing priority (normal, high, low)
            
        Returns:
            Dict with standardized process options
        """
        return {
            "build_knowledge_graph": build_knowledge_graph,
            "chunking_strategy": chunking_strategy,
            "use_batch_api": use_batch_api,
            "priority": priority
        }
    
    @staticmethod
    def create_document_metadata(
        source_type: str,
        source_identifier: Optional[str] = None,
        **additional_fields
    ) -> Dict[str, Any]:
        """
        Create standardized document metadata dictionary.
        
        Args:
            source_type: Type of source (upload, url, pubmed, etc.)
            source_identifier: Source identifier (e.g., PMID for PubMed)
            additional_fields: Additional metadata fields
            
        Returns:
            Dict with standardized document metadata
        """
        metadata = {
            "source": {
                "type": source_type,
            }
        }
        
        if source_identifier:
            metadata["source"]["identifier"] = source_identifier
            
        # Add any additional fields
        if additional_fields:
            metadata.update(additional_fields)
            
        return metadata


class TaskTransitionMap:
    """
    Defines allowed task status transitions.
    
    This class provides a mapping of allowed status transitions for different task types.
    It ensures that tasks follow a consistent and valid state progression.
    """
    
    # Define the base document processing flow transitions
    _BASE_TRANSITIONS = {
        TaskStatus.PENDING.value: [TaskStatus.CREATED.value, TaskStatus.QUEUED.value, TaskStatus.UPLOADING.value],
        TaskStatus.CREATED.value: [TaskStatus.QUEUED.value, TaskStatus.UPLOADING.value, TaskStatus.PROCESSING.value],
        TaskStatus.QUEUED.value: [TaskStatus.PROCESSING.value, TaskStatus.UPLOADING.value, TaskStatus.FAILED.value],
        TaskStatus.UPLOADING.value: [TaskStatus.UPLOADED.value, TaskStatus.FAILED.value],
        TaskStatus.UPLOADED.value: [TaskStatus.CHUNKING.value, TaskStatus.PROCESSING.value, TaskStatus.FAILED.value],
        TaskStatus.CHUNKING.value: [TaskStatus.CHUNKED.value, TaskStatus.CHUNKING_FAILED.value],
        TaskStatus.CHUNKED.value: [TaskStatus.EMBEDDING.value, TaskStatus.PROCESSED.value, TaskStatus.FAILED.value],
        TaskStatus.EMBEDDING.value: [TaskStatus.EMBEDDED.value, TaskStatus.FAILED.value],
        TaskStatus.EMBEDDED.value: [TaskStatus.PROCESSED.value, TaskStatus.BATCH_JOB_SUBMITTED.value, TaskStatus.FAILED.value],
        TaskStatus.PROCESSING.value: [TaskStatus.PROCESSED.value, TaskStatus.PROCESSING_FAILED.value, TaskStatus.FAILED.value],
        TaskStatus.PROCESSED.value: [TaskStatus.COMPLETED.value],
        TaskStatus.FAILED.value: [TaskStatus.RETRY.value],
        TaskStatus.CHUNKING_FAILED.value: [TaskStatus.RETRY.value],
        TaskStatus.PROCESSING_FAILED.value: [TaskStatus.RETRY.value],
    }
    
    # Knowledge graph specific transitions
    _KG_TRANSITIONS = {
        TaskStatus.PROCESSED.value: [TaskStatus.BATCH_JOB_SUBMITTED.value, TaskStatus.COMPLETED.value],
        TaskStatus.BATCH_JOB_SUBMITTED.value: [TaskStatus.BATCH_JOB_PROCESSING.value, TaskStatus.BATCH_JOB_FAILED.value],
        TaskStatus.BATCH_JOB_PROCESSING.value: [TaskStatus.BATCH_JOB_COMPLETED.value, TaskStatus.BATCH_JOB_FAILED.value],
        TaskStatus.BATCH_JOB_COMPLETED.value: [TaskStatus.KG_PROCESSING_STARTED.value, TaskStatus.FAILED.value],
        TaskStatus.KG_PROCESSING_STARTED.value: [TaskStatus.KG_PROCESSING_COMPLETED.value, TaskStatus.KG_FAILED.value],
        TaskStatus.KG_PROCESSING_COMPLETED.value: [TaskStatus.COMPLETED.value],
        TaskStatus.BATCH_JOB_FAILED.value: [TaskStatus.RETRY.value],
        TaskStatus.KG_FAILED.value: [TaskStatus.RETRY.value]
    }
    
    # Merge all transitions
    _ALL_TRANSITIONS = {**_BASE_TRANSITIONS, **_KG_TRANSITIONS}
    
    # Transitions for VERTEX_AI_BATCH_JOB
    _VERTEX_AI_BATCH_JOB_TRANSITIONS = {
        TaskStatus.CREATED.value: [TaskStatus.PROCESSING.value, TaskStatus.FAILED.value, TaskStatus.COMPLETED.value],
        TaskStatus.PROCESSING.value: [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value],
        TaskStatus.COMPLETED.value: [],
        TaskStatus.FAILED.value: [TaskStatus.RETRY.value]
    }

    # Update _ALL_TRANSITIONS to include VERTEX_AI_BATCH_JOB transitions
    # This ensures that if a key exists in multiple dictionaries, the last one (VERTEX_AI_BATCH_JOB_TRANSITIONS) takes precedence for those keys.
    _ALL_TRANSITIONS.update(_VERTEX_AI_BATCH_JOB_TRANSITIONS)

    @classmethod
    def get_allowed_transitions(cls, current_status: str, task_type: str = None) -> List[str]:
        """
        Get allowed status transitions from the current status.
        
        Args:
            current_status: Current task status
            task_type: Type of task (optional, for specialized transitions)
            
        Returns:
            List of allowed status transitions
        """
        # Get basic transitions for this status
        transitions = cls._ALL_TRANSITIONS.get(current_status, [])
        
        # Further filter based on task_type if needed
        if task_type and (task_type.startswith(TaskType.KNOWLEDGE_GRAPH.value) or task_type == TaskType.KNOWLEDGE_GRAPH_BUILDING.value):
            if current_status == TaskStatus.PROCESSED.value:
                # KG tasks must go through batch processing
                return [status for status in transitions if status != TaskStatus.COMPLETED.value]
        
        return transitions
    
    @classmethod
    def is_valid_transition(cls, current_status: str, new_status: str, task_type: str = None) -> bool:
        """
        Check if a status transition is valid.
        
        Args:
            current_status: Current task status
            new_status: New task status
            task_type: Type of task (optional, for specialized transitions)
            
        Returns:
            True if transition is valid, False otherwise
        """
        allowed = cls.get_allowed_transitions(current_status, task_type)
        return new_status in allowed