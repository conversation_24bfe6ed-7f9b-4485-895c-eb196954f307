"""
Configuration settings for the longevity platform.
"""
import os
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional


class Settings(BaseSettings):
    """Main settings for the application."""

    # API Settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    DEBUG: bool = True
    SECRET_KEY: str = "your_secret_key_here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Database Settings (loaded from environment variables or secrets)
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "longevity")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "longevitypass")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "longevity")
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST", "postgres")
    POSTGRES_PORT: int = int(os.getenv("POSTGRES_PORT", 5432))
    DATABASE_URL: Optional[str] =  f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"


    # Redis Settings
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: Optional[str] = None

    # Storage Settings
    MINIO_ROOT_USER: str = "minio"
    MINIO_ROOT_PASSWORD: str = "minio123"
    MINIO_HOST: str = "minio"
    MINIO_PORT: int = 9000
    MINIO_SECURE: bool = False
    STORAGE_BUCKET_NAME: str = "longevity-documents"
    STORAGE_URL: Optional[str] = None
    STORAGE_PROVIDER: str = "gcs"

    # Processing Settings
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    EMBEDDING_MODEL: str = "intfloat/e5-large-v2"
    EMBEDDING_DIMENSION: int = 1024
    MAX_DOCUMENT_SIZE_MB: int = 50

    # Vertex AI Settings
    GCS_PROJECT_ID: str = "rosy-rider-453708-c3"
    GOOGLE_APPLICATION_CREDENTIALS: Optional[str] = "gcs_login.json"
    VERTEX_AI_PROJECT_ID: Optional[str] = "rosy-rider-453708-c3"
    VERTEX_AI_LOCATION: str = "europe-west4"
    VERTEX_AI_MODEL_ID: str = "publishers/google/models/gemini-2.0-flash-001"
    VERTEX_AI_BATCH_MODEL_ID: str = "projects/rosy-rider-453708-c3/locations/europe-west4/publishers/google/models/gemini-2.0-flash-001"
    VERTEX_AI_MODEL_NAME: str = "google/gemini-2.0-flash-001"

    # Google Drive Settings
    GOOGLE_DRIVE_FOLDER_ID: Optional[str] = os.getenv("GOOGLE_DRIVE_FOLDER_ID", "11SzDukhfSxRI2JH-tdsltgwo2XfPcLfS")
    GDRIVE_CHECK_INTERVAL_SECONDS: int = 300

    # Google Cloud Storage Settings
    GCS_BUCKET_NAME: str = "longevity-batch-predictions"
    GCS_CREDENTIALS_FILE: str = "gcs_login.json"

    # Neo4j Settings
    NEO4J_HOST: str = "neo4j"
    NEO4J_PORT: int = 7687
    NEO4J_USER: Optional[str] = None
    NEO4J_PASSWORD: Optional[str] = None
    NEO4J_URI: Optional[str] = None
    NEO4J_AUTH: Optional[str] = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Parse NEO4J_AUTH if provided
        if self.NEO4J_AUTH:
            try:
                self.NEO4J_USER, self.NEO4J_PASSWORD = self.NEO4J_AUTH.split("/", 1)
            except ValueError:
                raise ValueError("NEO4J_AUTH must be in the format 'username/password'")

        # Build URLs if not provided
        if not self.DATABASE_URL:
            self.DATABASE_URL = f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

        if not self.REDIS_URL:
            if self.REDIS_PASSWORD:
                # URL encoding the password to handle special characters
                import urllib.parse
                encoded_password = urllib.parse.quote_plus(self.REDIS_PASSWORD)
                self.REDIS_URL = f"redis://:{encoded_password}@{self.REDIS_HOST}:{self.REDIS_PORT}/0"
            else:
                self.REDIS_URL = f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/0"

        if not self.STORAGE_URL:
            protocol = "https" if self.MINIO_SECURE else "http"
            self.STORAGE_URL = f"{protocol}://{self.MINIO_HOST}:{self.MINIO_PORT}"

        if not self.NEO4J_URI:
            self.NEO4J_URI = f"bolt://{self.NEO4J_HOST}:{self.NEO4J_PORT}"

    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=True
    )


# Create a global settings object
settings = Settings()


def get_settings():
    """Return the settings object."""
    return settings
