#!/usr/bin/env python3
"""
Validation script to test that our fixes are working.
"""

import sys
import os
import requests
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_task_type_enum():
    """Test that TaskType.KNOWLEDGE_GRAPH_BUILDING is available."""
    print("🧪 Testing TaskType enum...")
    try:
        from common.task_definitions import TaskType, TaskStatus
        
        # Test the specific enum value that was missing
        kg_building = TaskType.KNOWLEDGE_GRAPH_BUILDING
        print(f"✅ KNOWLEDGE_GRAPH_BUILDING: {kg_building.value}")
        
        # Test other related enums
        print(f"✅ VERTEX_AI_BATCH_JOB: {TaskType.VERTEX_AI_BATCH_JOB.value}")
        print(f"✅ KNOWLEDGE_GRAPH: {TaskType.KNOWLEDGE_GRAPH.value}")
        
        # Test TaskStatus enum
        print(f"✅ TaskStatus.CREATED: {TaskStatus.CREATED.value}")
        print(f"✅ TaskStatus.PROCESSING: {TaskStatus.PROCESSING.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ TaskType test failed: {e}")
        return False

def test_config_loading():
    """Test that Settings class loads without errors."""
    print("\n🧪 Testing configuration loading...")
    try:
        from common.config import settings
        
        print(f"✅ Settings loaded successfully")
        print(f"✅ APP_ENV: {settings.APP_ENV}")
        print(f"✅ API_PORT: {settings.API_PORT}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def test_data_transport_methods():
    """Test that DataTransport has the required methods."""
    print("\n🧪 Testing DataTransport methods...")
    try:
        from transport.data_transport import DataTransport
        
        # Check if methods exist
        transport = DataTransport()
        
        # Test method existence
        assert hasattr(transport, 'get_stale_tasks'), "get_stale_tasks method missing"
        assert hasattr(transport, 'get_pending_vertex_ai_batch_jobs'), "get_pending_vertex_ai_batch_jobs method missing"
        assert hasattr(transport, 'get_task_status_summary'), "get_task_status_summary method missing"
        
        print("✅ get_stale_tasks method exists")
        print("✅ get_pending_vertex_ai_batch_jobs method exists")
        print("✅ get_task_status_summary method exists")
        
        return True
        
    except Exception as e:
        print(f"❌ DataTransport test failed: {e}")
        return False

def test_api_connectivity():
    """Test that API is responding."""
    print("\n🧪 Testing API connectivity...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ API is responding (status: {health_data.get('status', 'unknown')})")
            
            # Check components
            components = health_data.get('components', {})
            for component, status in components.items():
                if 'unhealthy' in status:
                    print(f"⚠️  {component}: {status}")
                else:
                    print(f"✅ {component}: {status}")
            
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API connectivity test failed: {e}")
        return False

def test_worker_status():
    """Test worker container status."""
    print("\n🧪 Testing worker status...")
    try:
        import subprocess
        
        # Check if worker container is running
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=longevityco-worker", "--format", "{{.Status}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and "Up" in result.stdout:
            print("✅ Worker container is running")
            return True
        else:
            print(f"❌ Worker container status: {result.stdout.strip()}")
            return False
            
    except Exception as e:
        print(f"❌ Worker status test failed: {e}")
        return False

def main():
    """Run all validation tests."""
    print("🔍 Validating fixes for LongevityCo platform...")
    print("=" * 50)
    
    tests = [
        ("TaskType Enum", test_task_type_enum),
        ("Configuration Loading", test_config_loading),
        ("DataTransport Methods", test_data_transport_methods),
        ("API Connectivity", test_api_connectivity),
        ("Worker Status", test_worker_status)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}: FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All fixes are working correctly!")
        print("✅ The system is ready for comprehensive testing")
        return 0
    else:
        print(f"\n⚠️  {total - passed} issues still need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
