#!/usr/bin/env python
"""
Create test data for knowledge graph batch processing.

This script:
1. Creates a sample document with ID 11111111-1111-1111-1111-111111111111
2. Creates a sample task with ID *************-2222-2222-************
3. Creates sample chunks for the document

Usage:
    python ai/scripts/create_test_data.py
"""
import os
import sys
import uuid
import json
import argparse
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from common.config import settings


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Create test data for knowledge graph batch processing.")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    return parser.parse_args()


def main():
    """Run the script."""
    args = parse_args()
    
    # Override settings for testing
    settings.POSTGRES_USER = "longevity"
    settings.POSTGRES_PASSWORD = "longevity"
    settings.POSTGRES_DB = "longevity_test"
    settings.POSTGRES_HOST = "localhost"  # Connect to localhost from outside Docker
    settings.POSTGRES_PORT = 5435  # Mapped port from docker-compose.test.yml
    settings.DATABASE_URL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
    
    # Print the database URL for debugging
    print(f"Database URL: {settings.DATABASE_URL}")
    
    # Create a direct database connection
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db_session = SessionLocal()
    
    try:
        # Create a sample document with a fixed UUID
        document_id = uuid.UUID("11111111-1111-1111-1111-111111111111")
        
        # Check if the document already exists
        result = db_session.execute(
            text("SELECT id FROM documents WHERE id = :id"),
            {"id": document_id}
        ).fetchone()
        
        if result:
            print(f"Document with ID {document_id} already exists")
        else:
            # Create the document
            db_session.execute(
                text("INSERT INTO documents (id, title, content, source_url, document_type, doc_metadata, status) "
                "VALUES (:id, :title, :content, :source_url, :document_type, :doc_metadata, :status)"),
                {
                    "id": document_id,
                    "title": "Test Document",
                    "content": "Test content for knowledge graph batch processing",
                    "source_url": "https://example.com/test",
                    "document_type": "text",
                    "doc_metadata": json.dumps({"test": True}),
                    "status": "processed"
                }
            )
            db_session.commit()
            print(f"Created document with ID: {document_id}")
        
        # Create a sample task with a fixed UUID
        task_id = uuid.UUID("*************-2222-2222-************")
        
        # Check if the task already exists
        result = db_session.execute(
            text("SELECT id FROM processing_tasks WHERE id = :id"),
            {"id": task_id}
        ).fetchone()
        
        if result:
            print(f"Task with ID {task_id} already exists")
        else:
            # Create the task
            batch_job_id = "projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-12345678"
            db_session.execute(
                text("INSERT INTO processing_tasks (id, document_id, task_type, status, result) "
                "VALUES (:id, :document_id, :task_type, :status, :result)"),
                {
                    "id": task_id,
                    "document_id": document_id,
                    "task_type": "knowledge_graph",
                    "status": "ready_to_continue",
                    "result": json.dumps({"batch_job_id": batch_job_id})
                }
            )
            db_session.commit()
            print(f"Created task with ID: {task_id}")
        
        # Create sample chunks for the document
        # Check if chunks already exist
        result = db_session.execute(
            text("SELECT COUNT(*) FROM kg_chunks WHERE document_id = :document_id"),
            {"document_id": document_id}
        ).fetchone()
        
        if result and result[0] > 0:
            print(f"Chunks for document {document_id} already exist")
        else:
            # Create 5 sample chunks
            for i in range(5):
                chunk_id = uuid.uuid4()
                db_session.execute(
                    text("INSERT INTO kg_chunks (id, document_id, text, chunk_index, chunk_metadata) "
                    "VALUES (:id, :document_id, :text, :chunk_index, :chunk_metadata)"),
                    {
                        "id": chunk_id,
                        "document_id": document_id,
                        "text": f"Test chunk {i+1} for knowledge graph batch processing",
                        "chunk_index": i,
                        "chunk_metadata": json.dumps({"index": i})
                    }
                )
            db_session.commit()
            print(f"Created 5 chunks for document {document_id}")
        
        return 0
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    finally:
        # Close the database session
        db_session.close()


if __name__ == "__main__":
    sys.exit(main())
