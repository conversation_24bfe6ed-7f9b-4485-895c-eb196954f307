"""
<PERSON><PERSON><PERSON> to update the test database schema for the new embedding dimension.

This script:
1. Connects to the test database
2. Drops the existing chunks table
3. Recreates the chunks table with the new embedding dimension
4. Creates the pgvector extension if it doesn't exist

Usage:
    python ai/scripts/update_test_db_schema.py
"""
import sys
import os
import logging
from sqlalchemy import create_engine, text

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from common.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Override settings for test database
settings.POSTGRES_USER = "postgres"
settings.POSTGRES_PASSWORD = "postgres"
settings.POSTGRES_DB = "longevity_test"
settings.POSTGRES_HOST = "localhost"  # Connect to localhost from outside Docker
settings.POSTGRES_PORT = 5435  # Mapped port from docker-compose.test.yml

# Update the database URL
settings.DATABASE_URL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"

# Set the embedding dimension
settings.EMBEDDING_DIMENSION = 1024


def update_test_db_schema():
    """Update the test database schema for the new embedding dimension."""
    logger.info(f"Connecting to test database: {settings.DATABASE_URL}")
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as connection:
        # Create a transaction
        with connection.begin():
            # Create pgvector extension if it doesn't exist
            logger.info("Creating pgvector extension if it doesn't exist")
            connection.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            
            # Drop the existing chunks table
            logger.info("Dropping existing chunks table")
            connection.execute(text("DROP TABLE IF EXISTS chunks"))
            
            # Recreate the chunks table with the new embedding dimension
            logger.info(f"Recreating chunks table with embedding dimension: {settings.EMBEDDING_DIMENSION}")
            connection.execute(text(f"""
                CREATE TABLE chunks (
                    id UUID PRIMARY KEY,
                    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
                    chunk_index INTEGER NOT NULL,
                    text TEXT NOT NULL,
                    chunk_metadata JSONB,
                    embedding VECTOR({settings.EMBEDDING_DIMENSION}),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE (document_id, chunk_index)
                )
            """))
            
            # Create vector index for embeddings
            logger.info("Creating vector index for embeddings")
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chunks_embedding 
                ON chunks USING ivfflat (embedding vector_cosine_ops) 
                WITH (lists = 100)
            """))
    
    logger.info("Test database schema updated successfully")


if __name__ == "__main__":
    update_test_db_schema()
