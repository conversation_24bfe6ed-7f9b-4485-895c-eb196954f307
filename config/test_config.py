import os
import socket

def is_running_in_docker():
    """Check if the application is running inside a Docker container."""
    try:
        with open('/proc/1/cgroup', 'rt') as f:
            return 'docker' in f.read()
    except:
        return False

def get_storage_host():
    """Get the appropriate host name based on environment."""
    # If running in Docker or STORAGE_HOST env var is set, use it
    if is_running_in_docker() or os.environ.get('FORCE_DOCKER_HOSTS', '').lower() == 'true':
        return os.environ.get('MINIO_HOST', 'minio')
    
    # Otherwise use localhost for testing
    return 'localhost'

def configure_test_environment():
    """Configure environment variables for testing."""
    if os.environ.get('TESTING', '').lower() == 'true':
        os.environ['MINIO_HOST'] = get_storage_host()
        os.environ['MINIO_ENDPOINT'] = f"{get_storage_host()}:9000"
        
        # Other test configurations can be set here
        # Explicitly set the correct MinIO credentials and bucket name for testing
        os.environ['MINIO_ACCESS_KEY'] = 'minio'
        os.environ['MINIO_SECRET_KEY'] = 'minio123'
        os.environ['MINIO_BUCKET_NAME'] = 'test-longevity-documents'
